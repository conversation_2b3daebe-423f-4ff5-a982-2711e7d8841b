package com.fozmo.ym.module.auth.api.permission.dto;

import com.fozmo.ym.framework.common.enums.CommonStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Set;
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class RoleDataDTO {


    /**
     * 角色ID
     */
    private Long id;
    /**
     * 角色名称
     */
    private String name;
    /**
     * 角色标识
     * <p>
     * 枚举
     */
    private String code;
    /**
     * 角色排序
     */
    private Integer sort;
    /**
     * 角色状态
     * <p>
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;
    /**
     * 角色类型
     *
     */
    private Integer type;
    /**
     * 备注
     */
    private String remark;

    /**
     * 数据范围
     */
    private Integer dataScope;


    private Long tenantId;
    /**
     * 数据范围(指定部门数组)
     */
    private Set<Long> dataScopeDeptIds;
}
