package com.fozmo.ym.module.auth.api.permission;

import com.fozmo.ym.module.auth.api.permission.dto.RoleDataDTO;

import java.util.Collection;
import java.util.List;

/**
 * 角色 API 接口
 *
 *<AUTHOR>
 */
public interface RoleApi {

    /**
     * 校验角色们是否有效。如下情况，视为无效：
     * 1. 角色编号不存在
     * 2. 角色被禁用
     *
     * @param ids 角色编号数组
     */
    void validRoleList(Collection<Long> ids);

    List<RoleDataDTO> getRoleList();

    Long createRole(RoleDataDTO reqVO, Integer type);
}
