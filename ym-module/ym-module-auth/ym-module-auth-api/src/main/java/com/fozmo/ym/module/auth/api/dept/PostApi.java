package com.fozmo.ym.module.auth.api.dept;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.fozmo.ym.framework.common.util.collection.CollectionUtils;
import com.fozmo.ym.module.auth.api.dept.dto.PostRespDTO;
import com.fozmo.ym.module.auth.api.dept.dto.UserPostDTO;


import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 岗位 API 接口
 *
 * <AUTHOR>
 */
public interface PostApi {

    /**
     * 校验岗位们是否有效。如下情况，视为无效：
     * 1. 岗位编号不存在
     * 2. 岗位被禁用
     *
     * @param ids 岗位编号数组
     */
    void validPostList(Collection<Long> ids);

    PostRespDTO getPostById(Long id);

    List<PostRespDTO> getPostList(Collection<Long> ids);

    default Map<Long, PostRespDTO> getPostMap(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return MapUtil.empty();
        }

        List<PostRespDTO> list = getPostList(ids);
        return CollectionUtils.convertMap(list, PostRespDTO::getId);
    }

    void insertBatchUserPost(List<UserPostDTO> list);

    Collection<UserPostDTO> selectListByUserId(Long userId);

    void deleteByUserIdAndPostId(Long userId, Collection<Long> deletePostIds);

    void validatePostList(Set<Long> postIds);

    void deleteByUserId(Long id);


    List<UserPostDTO> selectListByPostIds(Collection<Long> postIds);
}
