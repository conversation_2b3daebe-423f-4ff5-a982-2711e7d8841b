package com.fozmo.ym.module.auth.api.dept.dto;

import com.fozmo.ym.framework.common.enums.CommonStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 部门 Response DTO
 *
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class DeptRespDTO {

    /**
     * 部门编号
     */
    private Long id;
    /**
     * 部门名称
     */
    private String name;
    /**
     * 父部门编号
     */
    private Long parentId;
    /**
     * 负责人的用户编号
     */
    private Long leaderUserId;
    /**
     * 部门状态
     * <p>
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;

}
