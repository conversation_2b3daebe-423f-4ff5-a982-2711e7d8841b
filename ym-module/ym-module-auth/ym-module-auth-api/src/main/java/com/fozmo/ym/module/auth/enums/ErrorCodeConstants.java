package com.fozmo.ym.module.auth.enums;

import com.fozmo.ym.framework.common.exception.ErrorCode;

/**
 * System 错误码枚举类
 * <p>
 * system 系统，使用 1-002-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== AUTH 模块 1-002-000-000 ==========
    ErrorCode AUTH_LOGIN_BAD_CREDENTIALS = new ErrorCode(110001, "登录失败，账号密码不正确");
    ErrorCode AUTH_LOGIN_USER_DISABLED = new ErrorCode(110002, "登录失败，账号被禁用");
    ErrorCode AUTH_LOGIN_CAPTCHA_CODE_ERROR = new ErrorCode(110003, "验证码不正确，原因：{}");
    ErrorCode AUTH_THIRD_LOGIN_NOT_BIND = new ErrorCode(110004, "未绑定账号，需要进行绑定");
    ErrorCode AUTH_MOBILE_NOT_EXISTS = new ErrorCode(110005, "手机号不存在");
    ErrorCode AUTH_REGISTER_CAPTCHA_CODE_ERROR = new ErrorCode(110006, "验证码不正确，原因：{}");


    // ========== 社交用户 1-002-018-000 ==========
    ErrorCode SOCIAL_USER_AUTH_FAILURE = new ErrorCode(110007, "社交授权失败，原因是：{}");
    ErrorCode SOCIAL_USER_NOT_EXISTS = new ErrorCode(110008, "游客信息不存在、绑定微信失败");
    ErrorCode SOCIAL_USER_NOT_FOUND = new ErrorCode(110009, "社交授权失败，找不到对应的用户");

    ErrorCode SOCIAL_USER_PHONE_CODE_ERROR = new ErrorCode(110010, "获得手机号失败");

    ErrorCode ACCOUNT_NOT_EXISTS = new ErrorCode(110011, "账户类型缺少，注册失败");

    ErrorCode ACCOUNT_NOT = new ErrorCode(110012, "账户不存在");
}
