package com.fozmo.ym.module.auth.api.dept.dto;

import com.fozmo.ym.framework.common.enums.CommonStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 岗位 Response DTO
 *
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class PostRespDTO {

    /**
     * 岗位序号
     */
    private Long id;
    /**
     * 岗位名称
     */
    private String name;
    /**
     * 岗位编码
     */
    private String code;
    /**
     * 岗位排序
     */
    private Integer sort;
    /**
     * 状态
     * <p>
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;

}
