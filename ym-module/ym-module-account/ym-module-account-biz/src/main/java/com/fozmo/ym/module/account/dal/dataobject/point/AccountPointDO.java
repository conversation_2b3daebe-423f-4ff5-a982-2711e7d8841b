package com.fozmo.ym.module.account.dal.dataobject.point;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 账户积分表
 */
@TableName("account_point") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountPointDO extends TenantBaseDO {
    @Schema(description = "主键、钱包id")
    private Long id;
    @Schema(description = "账户id")
    private Long accountId;

    @Schema(description = "积分")
    private Integer point;

    @Schema(description = "积分单位")
    private String pointUnit;

    @Schema(description = "积分标志 元宝")
    private String pointFlag;

    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updaterId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;



}
