package com.fozmo.ym.module.order.service.order;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.order.controller.admin.log.vo.*;
import com.fozmo.ym.module.order.dal.dataobject.order.OrderLogDO;

import java.util.List;

/**
 *
 *  订单日志
 */
public interface OrderLogService {


    /**
     * 插入订单日志
     */
    Integer insert(OrderLogSaveReqVO reqVO);


    /**
     * 删除订单日志
     */
    Integer delete(Long id);


    /**
     * 更新订单日志
     */
    Integer update(OrderLogUpdateReqVO reqVO);


    /**
     * 查询订单日志
     */
    OrderLogInfoResVO selectById(Long id);


    /**
     * 查询订单日志列表
     */
    List<OrderLogPageResVO> selectList(OrderLogListReqVO reqVO);


    /**
     *分页查询订单日志
     */
    PageResult<OrderLogPageResVO> selectPage(OrderLogPageReqVO orderLogPageReqVO);


}
