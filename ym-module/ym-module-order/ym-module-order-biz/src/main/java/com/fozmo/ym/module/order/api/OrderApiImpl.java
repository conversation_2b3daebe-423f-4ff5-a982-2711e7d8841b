package com.fozmo.ym.module.order.api;

import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.order.api.dto.CreateOrderDTO;
import com.fozmo.ym.module.order.controller.admin.order.vo.CreateOrderReqVO;
import com.fozmo.ym.module.order.controller.admin.order.vo.CreateOrderResVO;
import com.fozmo.ym.module.order.service.order.OrderService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class OrderApiImpl implements OrderApi {

    @Resource
    private OrderService orderService;
    /**
     * @param createOrderDTO
     * @return
     */
    @Override
    public Long createOrder(CreateOrderDTO createOrderDTO) {

        CreateOrderResVO createOrderResVO = orderService.createOrder(BeanUtils.toBean(createOrderDTO, CreateOrderReqVO.class));
        return createOrderResVO.getOrderId();
    }

    /**
     * @param orderId
     * @return
     */
    @Override
    public Boolean cancelOrder(Long orderId) {
        return orderService.cancelOrder(orderId);
    }


}
