package com.fozmo.ym.module.order.service.order;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.order.controller.admin.log.vo.*;
import com.fozmo.ym.module.order.dal.dataobject.order.OrderLogDO;
import com.fozmo.ym.module.order.dal.mysql.order.OrderLogMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OrderLogServiceImpl implements OrderLogService {
    @Resource
    private OrderLogMapper orderLogMapper;

    /**
     * 插入订单日志
     *
     * @param reqVO
     */
    @Override
    public Integer insert(OrderLogSaveReqVO reqVO) {

        OrderLogDO orderLogDO = BeanUtils.toBean(reqVO, OrderLogDO.class);
        return orderLogMapper.insert(orderLogDO);
    }

    /**
     * 删除订单日志
     *
     * @param id
     */
    @Override
    public Integer delete(Long id) {
        return orderLogMapper.deleteById(id);
    }

    /**
     * 更新订单日志
     *
     * @param reqVO
     */
    @Override
    public Integer update(OrderLogUpdateReqVO reqVO) {
        OrderLogDO orderLogDO = BeanUtils.toBean(reqVO, OrderLogDO.class);
        return orderLogMapper.updateById(orderLogDO);

    }

    /**
     * 查询订单日志
     *
     * @param id
     */
    @Override
    public OrderLogInfoResVO selectById(Long id) {
        OrderLogDO orderLogDO = orderLogMapper.selectById(id);
        return BeanUtils.toBean(orderLogDO, OrderLogInfoResVO.class);
    }

    /**
     * 查询订单日志列表
     *
     * @param reqVO
     */
    @Override
    public List<OrderLogPageResVO> selectList(OrderLogListReqVO reqVO) {
        return List.of();
    }

    /**
     * 分页查询订单日志
     *
     * @param orderLogPageReqVO
     */
    @Override
    public PageResult<OrderLogPageResVO> selectPage(OrderLogPageReqVO orderLogPageReqVO) {
        return null;
    }
}
