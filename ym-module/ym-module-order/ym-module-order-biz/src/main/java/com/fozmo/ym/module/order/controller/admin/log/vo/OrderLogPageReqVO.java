package com.fozmo.ym.module.order.controller.admin.log.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "订单进度分页参数")
public class OrderLogPageReqVO extends PageParam {
    @Schema(description = "订单id")
    private Long orderId;
    @Schema(description = "状态")
    private Integer status;
    @Schema(description = "订单名称")
    private String name;


}
