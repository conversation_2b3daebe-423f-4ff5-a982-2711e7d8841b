package com.fozmo.ym.module.order.controller.admin.log.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "订单进度分页结果")
public class OrderLogPageResVO {

    @Schema(description = "主键")
    private Long id;
    @Schema(description = "订单id")
    private Long orderId;
    @Schema(description = "订单名称")
    private String name;
    @Schema(description = "状态 支付和订单状态汇总")
    private Integer status;
    @Schema(description = "支付应用")
    private Long appId;
    @Schema(description = "渠道id")
    private Long channelId;
    @Schema(description = "渠道名称")
    private String channelCode;
    @Schema(description = "渠道扩展信息")
    private String channelExtras;
    @Schema(description = "渠道返回码")
    private String channelErrorCode;
    @Schema(description = "渠道返回消息")
    private String channelErrorMessage ;
    @Schema(description = "渠道回调")
    private String channelNotify;
    @Schema(description = "操作时间")
    private String operaTime;
    @Schema(description = "描述")
    private String description;
}
