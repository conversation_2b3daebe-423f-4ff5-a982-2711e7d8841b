package com.fozmo.ym.module.order.service.order;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.order.controller.admin.order.vo.*;

/***
 * 订单 服务
 */
public interface OrderService {

    /***
     *  创建订单
     */
    CreateOrderResVO createOrder(CreateOrderReqVO reqVO);

    /**
     *
     */

    /**
     * 取消订单
     */
    Boolean cancelOrder(Long orderId);


    /**
     * 获取订单详情
     */
    OrderInfoResVO getOrderDetail(Long orderId);

    /**
     * 分页查询订单
     */
    PageResult<OrderPageResVO> getOrderPage(OrderPageReqVO reqVO);
    

}
