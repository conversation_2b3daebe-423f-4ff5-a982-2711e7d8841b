<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fozmo</groupId>
        <artifactId>ym-module-space</artifactId>
        <version>1.0</version>
    </parent>

    <artifactId>ym-module-space-api</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
   <dependencies>
       <dependency>
           <groupId>com.fozmo</groupId>
           <artifactId>ym-common</artifactId>
       </dependency>
       <dependency>
           <groupId>io.swagger.core.v3</groupId>
           <artifactId>swagger-models-jakarta</artifactId>
           <version>2.2.30</version>
       </dependency>
       <dependency>
           <groupId>io.swagger.core.v3</groupId>
           <artifactId>swagger-annotations-jakarta</artifactId>
           <version>2.2.30</version>
       </dependency>
   </dependencies>


</project>