package com.fozmo.ym.module.space.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "卡纸属性")
public class SpaceCardDTO {
    @Schema(description = "卡纸Id")
    private Long id;
    @Schema(description = "卡纸名称")
    private String name;
    @Schema(description = "卡纸编码")
    private String code;
    @Schema(description = "卡纸颜色")
    private String color;
    @Schema(description = "使用类型 1 普通 2 付费 3 权益")
    private Integer useType;

    @Schema(description = "使用类型Id 普通为空 付费商品Id 权益为权益Id")
    private Long useTypeId;

    @Schema(description = "卡纸纹理")
    private List<SpaceCardFigureDTO> figures;
}
