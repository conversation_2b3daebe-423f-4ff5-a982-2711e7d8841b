package com.fozmo.ym.module.space.constans;

public interface SpaceRedisConstants {

    String SPACE_EDITING_LOCK = "lock:editing:key:%d";

    String SPACE_EDITING_KEY = "ym:editing:space:";

    String WORKS_EDITING_KEY = "ym:editing:works:";

    String SPACE_MOUNT_KEY = "ym:mount:space:";

    String WORKS_MOUNT_KEY = "ym:mount:works:";

    String SPACE_EDITING_INFO_KEY = "space:editingInfo:key:%d";
    
    String SPACE_INFO_KEY= "ym:space:info";

    String LOCK_SPACE_DELETE_KEY = "lock:space:delete:";

    String LOCK_WORKS_DELETE_KEY = "lock:works:delete:";

    String LOCK_MOUNT_KEY = "lock:mount:key:";
    
    String SPACE_ACCOUNT_KEY = "ym:space:account:";
    
    String TEMPLATE_INFO_KEY = "ym:template:info";
    
    String TEMPLATE_TYPE_KEY = "ym:template:type";
    
    String TEMPLATE_TAG_KEY = "ym:template:tag";

    String WORKS_INFO_KEY = "ym:works:info";

    String WORKS_ACCOUNT_KEY = "ym:works:account:";

    String FILE_INFO_KEY = "ym:file:info";

    String FILE_TYPE_KEY = "ym:file:type";

    String HOT_INFO_KEY = "ym:hot:type:";

    
}
