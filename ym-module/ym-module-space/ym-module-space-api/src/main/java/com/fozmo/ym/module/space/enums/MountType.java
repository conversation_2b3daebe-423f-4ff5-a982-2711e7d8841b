package com.fozmo.ym.module.space.enums;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
public enum MountType {
    // 作品类 100-99
    IMAGE(1, "图片"),
    VIDEO(2, "视频"),
    MODEL(3, "模型"),
    GIF(4, "动图"),

    // 素材类 101-200
    FILE_MODEL(101, "模型素材"),
    FILE_TEXT(102, "3d文字素材"),
    FILE_GIF(104, "动图素材"),
    FILE_IMAGE(105, "图片素材"),

    // 音频类  301-400
    FILE_MUSIC(301, "音乐文件"),
    FILE_VIDEO(302, "视频文件"),

    // 特殊类型
    UNKNOWN(0, "未知类型");

    private static final Map<Integer, MountType> CODE_MAP;
    private static final Set<Integer> WORKS_TYPES;
    private static final Set<Integer> RESOURCE_TYPES;
    private static final Set<Integer> META_TYPES;

    static {
        // 初始化code到枚举的映射
        CODE_MAP = Collections.unmodifiableMap(
                Arrays.stream(values())
                        .collect(Collectors.toMap(MountType::getCode, Function.identity()))
        );

        // 初始化作品类型集合
        WORKS_TYPES = Collections.unmodifiableSet(
                Arrays.stream(values())
                        .filter(e -> e.code >= 1 && e.code <= 99) // 使用code范围判断
                        .map(MountType::getCode)
                        .collect(Collectors.toSet())
        );

        // 初始化资源类型集合
        RESOURCE_TYPES = Collections.unmodifiableSet(
                Arrays.stream(values())
                        .filter(e -> e.code >= 101 && e.code < 200) // 使用code范围判断
                        .map(MountType::getCode)
                        .collect(Collectors.toSet())
        );

        // 初始化元数据类型集合（当前为空）
        META_TYPES = Collections.unmodifiableSet(
                Arrays.stream(values())
                        .filter(e -> e.code >= 301 && e.code < 400) // 使用code范围判断
                        .map(MountType::getCode)
                        .collect(Collectors.toSet())
        );
    }

    private final int code;
    private final String description;

    MountType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static MountType fromCode(Integer code) {
        if (code == null) {
            return UNKNOWN;
        }
        return CODE_MAP.getOrDefault(code, UNKNOWN);
    }

    public static boolean isWorksType(int code) {
        return WORKS_TYPES.contains(code);
    }

    public static boolean isResourceType(int code) {
        return RESOURCE_TYPES.contains(code);
    }

    public static boolean isMetaType(int code) {
        return META_TYPES.contains(code);
    }

    public static Set<Integer> worksTypes() {
        return WORKS_TYPES;
    }

    public static Set<Integer> resourceTypes() {
        return RESOURCE_TYPES;
    }

    public static Set<Integer> metaTypes() {
        return META_TYPES;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 修复了未完成的if语句
    public boolean isWorksType() {
        return WORKS_TYPES.contains(code);
    }

    public boolean isResourceType() {
        return RESOURCE_TYPES.contains(code);
    }

    public boolean isMetaType() {
        return META_TYPES.contains(code);
    }


    @Override
    public String toString() {
        return description + "(" + code + ")";
    }
}