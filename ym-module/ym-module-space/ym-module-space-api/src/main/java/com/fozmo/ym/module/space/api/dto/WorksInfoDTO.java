package com.fozmo.ym.module.space.api.dto;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;
@Data
public class WorksInfoDTO {

    private Long id;
    /**
     * 作品状态： 0初始化 1布置  9占用状态
     */
    private Integer worksStatus;
    /**
     * 高精度路径
     */
    private String hignUrl;
    /**
     * 标精路径
     */
    private String lowUrl;
    /**
     * 创建人
     */
    private Long accountId;
    /**
     * 挂载点
     */
    private Integer point;
    /**
     * 挂点类型 1图片，2视频，3 3D模型，4 动图，5 3D文字
     */
    private Integer pointType;
    /**
     * 文本内容
     */
    private String textContent;
    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;

    private String worksCnName;

    private String worksEnName;

    private String worksCover;
    /**
     * 更新人
     */
    private Long updaterId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;

    /**
     * 中文说明
     */
    private String cnDescription;
    /**
     * 英文说明
     */
    private String enDescription;


    private String worksAuthor;

    private Integer worksWidth;

    private Integer worksHeight;

   
    private Long commentCount;
   
    private Boolean commentStatus;
   
    private Long likeCount;
   
    private Boolean likeStatus;
   
    private Long shareCount;
   
    private Boolean shareStatus;
   
    private Long favoriteCount;
   
    private Boolean favoriteStatus;
   
    private String accountName;
   
    private String nickname;
   
    private String avatar;
   
    private Long rightsId;
   
    private String rightsName;
    
    private List<WorksUsageDTO> usage;
    private Integer usageCount;
    
    
}
