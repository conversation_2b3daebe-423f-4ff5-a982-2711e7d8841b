package com.fozmo.ym.module.space.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "画框尺寸属性")
public class SpaceFrameSizeDTO {
    @Schema(description = "画框尺寸id")
    private Long id;
    @Schema(description = "画框尺寸名称")
    private String name;
    @Schema(description = "画框尺寸编码  上下 top bottom 左右 left right")
    private String code;
    @Schema(description = "画框尺寸值")
    private String size;
    @Schema(description = "画框尺寸单位")
    private String sizeUnit;
}
