package com.fozmo.ym.module.space.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MountInfoDTO {

    @Schema(description = "挂载对象id", example = "29611")
    private Long mountId;
    private Long spaceId;
    @Schema(description = "挂载对象类型", example = "29611")
    private Integer mountType;

    private String mountName;

    private String mountDescription;

    @Schema(description = "扩展数据", example = "13935")
    private String extraData;
    @Schema(description = "权益Id", example = "13935")
    private Long rightsId;
    @Schema(description = "账户id", example = "13935")
    private Long accountId;
    @Schema(description = "账户名称", example = "13935")
    private String accountName;
    @Schema(description = "昵称", example = "13935")
    private String nickname;

    private Integer point;

    private Integer mountSort;

    private String hignUrl;

    private String lowUrl;

    private String MountCover;

    private String author;

    private Integer mountWidth;

    private Integer mountHeight;

    private Long id;
}
