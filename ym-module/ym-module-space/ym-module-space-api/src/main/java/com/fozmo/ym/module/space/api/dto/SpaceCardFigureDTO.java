package com.fozmo.ym.module.space.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "卡纸纹理属性")
public class SpaceCardFigureDTO {
    @Schema(description = "纹理Id")
    private Long id;
    @Schema(description = "纹理名称")
    private String name;
    @Schema(description = "纹理编码")
    private String code;
    @Schema(description = "纹理颜色")
    private String color;
    @Schema(description = "纹理图片")
    private String icon;


}
