package com.fozmo.ym.module.space.api.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SpaceInfoDTO {

    private Long id;
    /**
     * 空间模板id
     */
    private Long spaceTemplateId;
    /**
     * 空间中文名称
     */
    private String spaceCnName;
    /**
     * 空间英文名称
     */
    private String spaceEnName;
    /**
     * 空间码值
     */
    private String spaceCode;
    /**
     * 空间类型
     */
    private Long spaceTypeId;
    /**
     * 2d挂载点
     */
    private Integer spaceNum2;
    /**
     * 3d挂载点
     */
    private Integer spaceNum3;
    /**
     * 账户id
     */
    private Long accountId;
    /**
     * 账户编码
     */
    private String accountCode;
    /**
     * 中文说明
     */
    private String cnDescription;
    /**
     * 英文说明
     */
    private String enDescription;
    /**
     * 状态 0 初始化 1启用 2 停用 3 删除
     */
    private Integer spaceStatus;
    /**
     * 空间封面
     */
    private String spaceCover;
    /**
     * 一键复制 0 不允许 1运行
     */
    private Integer copyFlag;
    /**
     * 0 全部可见
     */
    private Integer privacyFlag;


    private Long worksCount;

    private Long likeCount;

    private Long commentCount;

    private Long viewCount;

    private Long shareCount;
    
    private String sponsor;
    
    private LocalDateTime exhibitionStartTime;
    
    private LocalDateTime exhibitionEndTime;
    
    private Integer exhibitionTimeStatus;


}
