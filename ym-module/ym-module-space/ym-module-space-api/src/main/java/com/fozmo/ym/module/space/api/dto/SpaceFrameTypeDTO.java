package com.fozmo.ym.module.space.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "画框类型属性")
public class SpaceFrameTypeDTO {
    @Schema(description = "画框类型id")
    private Long id;
    @Schema(description = "画框类型名称")
    private String name;
    @Schema(description = "画框类型编码 现代 modern 简约 simple")
    private String code;
    @Schema(description = "画框类型图标")
    private String icon;
}
