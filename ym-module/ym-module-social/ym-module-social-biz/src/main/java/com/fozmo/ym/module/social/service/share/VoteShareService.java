package com.fozmo.ym.module.social.service.share;

import com.fozmo.ym.framework.common.pojo.PageResult;

import com.fozmo.ym.module.social.controller.admin.share.vo.VoteSharePageReqVO;
import com.fozmo.ym.module.social.controller.admin.share.vo.VoteShareRespVO;
import com.fozmo.ym.module.social.controller.admin.share.vo.VoteShareSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.share.VoteShareDO;
import jakarta.validation.Valid;

/**
 * 活动分享 Service 接口
 *
 * <AUTHOR>
 */
public interface VoteShareService {

    /**
     * 创建投票活动分享
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    VoteShareRespVO createShare(@Valid VoteShareSaveReqVO createReqVO);

    /**
     * 更新投票活动分享
     *
     * @param updateReqVO 更新信息
     */
    void updateShare(@Valid VoteShareSaveReqVO updateReqVO);

    /**
     * 删除投票活动分享
     *
     * @param id 编号
     */
    void deleteShare(Long id);

    /**
     * 获得投票活动分享
     *
     * @param id 编号
     * @return 投票活动分享
     */
    VoteShareDO getShare(Long id);

    VoteShareDO getShareByVoteId(Long spaceId, Long accountId);

    VoteShareDO getShareByScene(String scene);

    /**
     * 获得投票活动分享分页
     *
     * @param pageReqVO 分页查询
     * @return 投票活动分享分页
     */
    PageResult<VoteShareDO> getSharePage(VoteSharePageReqVO pageReqVO);

    Long countByVoteId(Long voteId);

    boolean isShare(Long voteId, Long accountId);

    void deleteByVoteId(Long voteId);
}