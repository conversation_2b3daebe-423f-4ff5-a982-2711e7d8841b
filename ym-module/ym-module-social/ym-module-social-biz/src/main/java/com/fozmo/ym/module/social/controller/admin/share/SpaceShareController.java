package com.fozmo.ym.module.social.controller.admin.share;//package com.fozmo.ym.module.social.controller.admin.share;
//
//import org.springframework.web.bind.annotation.*;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import jakarta.validation.constraints.*;
//import jakarta.validation.*;
//import jakarta.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.fozmo.ym.framework.common.pojo.PageParam;
//import com.fozmo.ym.framework.common.pojo.PageResult;
//import com.fozmo.ym.framework.common.pojo.CommonResult;
//import com.fozmo.ym.framework.common.util.object.BeanUtils;
//import static com.fozmo.ym.framework.common.pojo.CommonResult.success;
//
//import com.fozmo.ym.framework.excel.core.util.ExcelUtils;
//
//import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
//import static com.fozmo.ym.framework.apilog.core.enums.OperateTypeEnum.*;
//
//import com.fozmo.ym.module.social.controller.admin.share.vo.*;
//import com.fozmo.ym.module.social.dal.dataobject.share.SpaceShareDO;
//import com.fozmo.ym.module.social.service.share.SpaceShareService;
//
//@Tag(name = "后管-空间模块 - 空间分享")
//@RestController
//@RequestMapping("/space/share")
//@Validated
//public class SpaceShareController {
//
//    @Resource
//    private SpaceShareService shareService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建空间分享")
//    @PreAuthorize("@ss.hasPermission('space:share:create')")
//    public CommonResult<SpaceShareRespVO> createShare(@Valid @RequestBody SpaceShareSaveReqVO createReqVO) {
//        return success(shareService.createShare(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新空间分享")
//    @PreAuthorize("@ss.hasPermission('space:share:update')")
//    public CommonResult<Boolean> updateShare(@Valid @RequestBody SpaceShareSaveReqVO updateReqVO) {
//        shareService.updateShare(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除空间分享")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('space:share:delete')")
//    public CommonResult<Boolean> deleteShare(@RequestParam("id") Long id) {
//        shareService.deleteShare(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得空间分享")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('space:share:query')")
//    public CommonResult<SpaceShareRespVO> getShare(@RequestParam("id") Long id) {
//        SpaceShareDO share = shareService.getShare(id);
//        return success(BeanUtils.toBean(share, SpaceShareRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得空间分享分页")
//    @PreAuthorize("@ss.hasPermission('space:share:query')")
//    public CommonResult<PageResult<SpaceShareRespVO>> getSharePage(@Valid SpaceSharePageReqVO pageReqVO) {
//        PageResult<SpaceShareDO> pageResult = shareService.getSharePage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, SpaceShareRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出空间分享 Excel")
//    @PreAuthorize("@ss.hasPermission('space:share:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportShareExcel(@Valid SpaceSharePageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<SpaceShareDO> list = shareService.getSharePage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "空间分享.xls", "数据", SpaceShareRespVO.class,
//                        BeanUtils.toBean(list, SpaceShareRespVO.class));
//    }
//
//}