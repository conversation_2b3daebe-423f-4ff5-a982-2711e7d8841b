package com.fozmo.ym.module.social.controller.admin.share.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 空间分享 Response VO")
@Accessors(chain = true)
@Data
public class VoteShareRespVO {

    @Schema(description = "id", example = "10513")
    private Long id;

    @Schema(description = "空间id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25451")
    private Long spaceId;

    @Schema(description = "分享id", example = "18333")
    private Long accountId;

    @Schema(description = "分享人名称", example = "芋艿")
    private String accountName;

    @Schema(description = "分享时间")
    private LocalDateTime shareTime;

    @Schema(description = "分享码值")
    private String shareCode;

    @Schema(description = "分享渠道 0 微信 1 抖音 2 其他")
    private Integer shareChannel;

    private String shareQrcode;
    
    private String sharePage;
    
    private String env;
    
    private String scene;
    
    private Long spaceAccountId;

    private String spaceCover;
    
    private Long spaceRightsId;
    
    private String spaceAccountName;
    
    private String spaceAccountCover;
    
    private String spaceName;
    @Schema(description = "创建人id", example = "9064")
    private Long createId;

    @Schema(description = "创建用户名称", example = "张三")
    private String creator;

    @Schema(description = "创建日期")
    private LocalDate createData;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人", example = "14851")
    private Long updateId;

    @Schema(description = "更新人", example = "王五")
    private String updater;
    @Schema(description = "更新日期")
    private LocalDate updateData;
    @Schema(description = "租户Code")
    private String tenantCode;

}