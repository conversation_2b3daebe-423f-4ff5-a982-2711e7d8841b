package com.fozmo.ym.module.social.service.share;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.mybatis.core.util.IdService;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.security.core.util.SecurityFrameworkUtils;
import com.fozmo.ym.module.account.api.account.AccountApi;
import com.fozmo.ym.module.account.api.account.dto.AccountBaseInfoDTO;
import com.fozmo.ym.module.auth.api.WechatApi;
import com.fozmo.ym.module.market.api.VoteApi;
import com.fozmo.ym.module.market.api.dto.VoteBaseInfoDTO;

import com.fozmo.ym.module.social.controller.admin.share.vo.VoteSharePageReqVO;
import com.fozmo.ym.module.social.controller.admin.share.vo.VoteShareRespVO;
import com.fozmo.ym.module.social.controller.admin.share.vo.VoteShareSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.share.SpaceShareDO;
import com.fozmo.ym.module.social.dal.dataobject.share.VoteShareDO;
import com.fozmo.ym.module.social.dal.mysql.share.VoteShareMapper;
import com.fozmo.ym.module.social.enums.ErrorCodeConstants;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

import static com.fozmo.ym.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.fozmo.ym.module.market.enums.ErrorCodeConstants.VOTE_NOT_EXISTS;

@Service
public class VoteShareServiceImpl implements VoteShareService{

    @Resource
    private VoteShareMapper voteShareMapper;


    @Resource
    private AccountApi accountApi;


    @Resource
    private IdService idService;

    @Resource
    private WechatApi wechatApi;

    @Resource
    private VoteApi voteApi;

    /**
     * 创建投票活动分享
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    @Override
    public VoteShareRespVO createShare(VoteShareSaveReqVO createReqVO) {
        // 获取当前用户权益
        Long accountId = SecurityFrameworkUtils.getLoginUserId();
        if (ObjectUtil.isEmpty(accountId)){
            accountId = -1L;
        }

        Long voteId = createReqVO.getVoteId();
        // todo 判断活动是否存在
        VoteBaseInfoDTO voteBaseInfoDTO = voteApi.getVoteBaseInfo(voteId);
        if (ObjectUtil.isEmpty(voteBaseInfoDTO)){
            throw exception(VOTE_NOT_EXISTS);
        }

        // 判断活动是否已经被分享
        VoteShareDO voteShareDO = voteShareMapper.getShareByVoteId(voteId,accountId);

        if (ObjectUtil.isNotEmpty(voteShareDO)){
            return BeanUtils.toBean(voteShareDO, VoteShareRespVO.class);
        }else {
            VoteShareDO share = BeanUtils.toBean(createReqVO, VoteShareDO.class);
            share.setAccountId(accountId);

            AccountBaseInfoDTO voteAccount = accountApi.queryAccountBaseInfoById(accountId);
            String sharCode = IdUtil.simpleUUID();
            String qrcode = wechatApi.createQRCode(createReqVO.getSharePage(),createReqVO.getScene(),createReqVO.getEnv());

            share.setId(idService.nextId("voteShare"));
            share.setShareCode(sharCode);
            share.setShareQrcode(qrcode);
            share.setVoteAccountId(voteAccount.getId());

            share.setCreateTime(LocalDateTimeUtil.now());
            share.setCreateData(LocalDate.now());
            share.setTenantId(1L);
            share.setVoteCover(voteBaseInfoDTO.getVoteCover());
            share.setVoteAccountId(voteAccount.getId());
            share.setVoteAccountName(voteAccount.getNickname());
            share.setVoteAccountCover(voteAccount.getAvatar());
            share.setVoteName(voteBaseInfoDTO.getVoteName());
            share.setSpaceRightsId(voteAccount.getRightsId());
            AccountBaseInfoDTO baseInfoDTO = accountApi.queryAccountBaseInfoById(accountId);
            if (ObjectUtil.isEmpty(baseInfoDTO)) {
                share.setCreator("游客");
                share.setCreateId(accountId);
                share.setAccountId(accountId);
                share.setAccountName("游客");
            } else {
                share.setCreator(baseInfoDTO.getName());
                share.setCreateId(baseInfoDTO.getId());
                share.setAccountId(baseInfoDTO.getId());
                share.setAccountName(baseInfoDTO.getName());
            }
            voteShareMapper.insert(share);

            return BeanUtils.toBean(share, VoteShareRespVO.class);
        }



    }

    /**
     * 更新投票活动分享
     *
     * @param updateReqVO 更新信息
     */
    @Override
    public void updateShare(VoteShareSaveReqVO updateReqVO) {
        // 校验存在
        validateShareExists(updateReqVO.getId());
        // 更新
        VoteShareDO updateObj = BeanUtils.toBean(updateReqVO, VoteShareDO.class);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("用户未登录");
        }
        updateObj.setUpdater(loginUser.getUsername());
        updateObj.setUpdateId(loginUser.getId());
        updateObj.setUpdateTime(LocalDateTimeUtil.now());
        voteShareMapper.updateById(updateObj);
    }

    /**
     * 删除投票活动分享
     *
     * @param id 编号
     */
    @Override
    public void deleteShare(Long id) {
        // 校验存在
        validateShareExists(id);
        // 删除
        voteShareMapper.deleteById(id);
    }

    private void validateShareExists(Long id) {
        if (voteShareMapper.selectById(id) == null) {
            throw exception(ErrorCodeConstants.SHARE_NOT_EXISTS);
        }
    }

    /**
     * 获得投票活动分享
     *
     * @param id 编号
     * @return 投票活动分享
     */
    @Override
    public VoteShareDO getShare(Long id) {
        return null;
    }

    /**
     * @param voteId
     * @param accountId
     * @return
     */
    @Override
    public VoteShareDO getShareByVoteId(Long voteId, Long accountId) {
        return null;
    }

    /**
     * @param scene
     * @return
     */
    @Override
    public VoteShareDO getShareByScene(String scene) {
        return null;
    }

    /**
     * 获得投票活动分享分页
     *
     * @param pageReqVO 分页查询
     * @return 投票活动分享分页
     */
    @Override
    public PageResult<VoteShareDO> getSharePage(VoteSharePageReqVO pageReqVO) {
        return null;
    }

    /**
     * @param voteId
     * @return
     */
    @Override
    public Long countByVoteId(Long voteId) {
        return 0L;
    }

    /**
     * @param voteId
     * @param accountId
     * @return
     */
    @Override
    public boolean isShare(Long voteId, Long accountId) {
        return false;
    }

    /**
     * @param voteId
     */
    @Override
    public void deleteByVoteId(Long voteId) {

    }
}
