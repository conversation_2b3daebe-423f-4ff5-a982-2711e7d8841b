package com.fozmo.ym.module.social.controller.app.share;

import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.framework.web.core.util.WebFrameworkUtils;
import com.fozmo.ym.module.social.controller.admin.share.vo.*;
import com.fozmo.ym.module.social.controller.app.share.vo.AppVoteSharePageReqVO;
import com.fozmo.ym.module.social.controller.app.share.vo.AppVoteShareRespVO;
import com.fozmo.ym.module.social.controller.app.share.vo.AppVoteShareSaveReqVO;
import com.fozmo.ym.module.social.dal.dataobject.share.VoteShareDO;
import com.fozmo.ym.module.social.service.share.VoteShareService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.fozmo.ym.framework.common.pojo.CommonResult.success;

@Tag(name = "APP—社交模块 - 投票活动分享")
@RestController
@RequestMapping("/social/vote")
@Validated
public class AppVoteShareController {


    @Resource
    private VoteShareService shareService;

    @PostMapping("/create")
    @Operation(summary = "创建投票活动分享")
    @PermitAll
    @ApiAccessLog
    public CommonResult<AppVoteShareRespVO> createShare(@Valid @RequestBody AppVoteShareSaveReqVO createReqVO) {

        VoteShareSaveReqVO reqVO =BeanUtils.toBean(createReqVO, VoteShareSaveReqVO.class);
        VoteShareRespVO info = shareService.createShare(reqVO);
        return success(BeanUtils.toBean(info, AppVoteShareRespVO.class));
    }

    @PutMapping("/update")
    @Operation(summary = "更新投票活动分享")
    @PermitAll
    @ApiAccessLog
    public CommonResult<Boolean> updateShare(@Valid @RequestBody AppVoteShareSaveReqVO updateReqVO) {
        VoteShareSaveReqVO reqVO =BeanUtils.toBean(updateReqVO, VoteShareSaveReqVO.class);
        shareService.updateShare(reqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除投票活动分享")
    @Parameter(name = "id", description = "编号", required = true)
    @PermitAll
    @ApiAccessLog
    public CommonResult<Boolean> deleteShare(@RequestParam("id") Long id) {
        shareService.deleteShare(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得投票活动分享")
    @Parameter(name = "scene", description = "编号", required = true, example = "scene")
    @PermitAll
    @ApiAccessLog
    public CommonResult<AppVoteShareRespVO> getShare(@RequestParam("scene") String scene) {
        VoteShareDO share = shareService.getShareByScene(scene);
        return success(BeanUtils.toBean(share, AppVoteShareRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得投票活动分享分页")
    @PermitAll
    @ApiAccessLog
    public CommonResult<PageResult<VoteShareRespVO>> getSharePage(@Valid AppVoteSharePageReqVO pageReqVO) {
        VoteSharePageReqVO reqVO = BeanUtils.toBean(pageReqVO, VoteSharePageReqVO.class);
        reqVO.setAccountId(WebFrameworkUtils.getLoginUserId());
        PageResult<VoteShareDO> pageResult = shareService.getSharePage(reqVO);
        return success(BeanUtils.toBean(pageResult, VoteShareRespVO.class));
    }

}
