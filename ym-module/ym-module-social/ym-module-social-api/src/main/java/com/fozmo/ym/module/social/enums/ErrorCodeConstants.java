package com.fozmo.ym.module.social.enums;

import com.fozmo.ym.framework.common.exception.ErrorCode;


public interface ErrorCodeConstants {
    ErrorCode COMMENT_NOT_EXISTS = new ErrorCode(820000, "空间评论不存在");
    ErrorCode COMMENT_NOT_CREATE = new ErrorCode(820001, "不允许空间评论");
    ErrorCode COMMENT_TYPE_NOT_EXISTS = new ErrorCode(820002, "空间评论类型不存在");

    ErrorCode FANS_ACCOUNT_NOT = new ErrorCode(820003, "关注时账户不可为null");

    ErrorCode FANS_HAVE = new ErrorCode(820004, "已关注，不可重复");

    ErrorCode FANS_NOT_EXISTS = new ErrorCode(820005, "未关注");

    ErrorCode SOCIAL_PLATFORM_EXISTS = new ErrorCode(820006, "社交平台已存在");

    ErrorCode SOCIAL_PLATFORM_NOT_EXISTS = new ErrorCode(820007, "社交平台不存在");


    ErrorCode FAVORITE_NOT_EXISTS = new ErrorCode(820010, "空间收藏不存在");
    ErrorCode LIKE_NOT_EXISTS = new ErrorCode(820011, "点赞不存在");
    ErrorCode LIKE_NOT_CREATE = new ErrorCode(820012, "已点赞");
    ErrorCode SHARE_NOT_EXISTS = new ErrorCode(820013, "空间分享不存在");

    
    ErrorCode WORKS_NOT_EXISTS = new ErrorCode(879999, "空间作品不存在");
    ErrorCode WORKS_COMMENT_NOT_EXISTS = new ErrorCode(879998, "作品评论不存在");
    
    ErrorCode WORKS_FAVORITE_NOT_EXISTS = new ErrorCode(879997, "作品收藏不存在");
    
    ErrorCode WORKS_SHARE_NOT_EXISTS = new ErrorCode(879996, "作品分享不存在");
    ErrorCode WORKS_SHARE_NOT_CREATE = new ErrorCode(879986, "作品分享无法创建");
    ErrorCode WORKS_LIKE_NOT_EXISTS = new ErrorCode(879995, "作品点赞不存在");
    
    ErrorCode FAVORITE_NOT_ONE = new ErrorCode(879994, "该空间已收藏，请勿重复收藏");
    
    ErrorCode MOUNT_WORKS_NOT_EXISTS = new ErrorCode(879993, "空间挂在作品不存在");
    
    ErrorCode EDITING_NOT_EXISTS = new ErrorCode(879992, "空间编辑id不存在");
    
    ErrorCode TEMPLATE_ACCOUNT_NOT_EXISTS = new ErrorCode(879991, "空间未指定账户");
    
    
    ErrorCode FILE_UP_TYPE_NOT_EXISTS = new ErrorCode(879990, "上传资源类型未指定");
    
    ErrorCode WORKS_CREATE_NO = new ErrorCode(878990, "不允许新增作品");
}
