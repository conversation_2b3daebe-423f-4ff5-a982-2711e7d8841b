<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fozmo</groupId>
        <artifactId>ym-module-log</artifactId>
        <version>1.0</version>
    </parent>

    <artifactId>ym-module-log-biz</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-log-api</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.mouzt</groupId>
            <artifactId>bizlog-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-auth-api</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-biz-ip</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-excel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
    </dependencies>

</project>