package com.fozmo.ym.module.log.framework.operatelog.core;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.fozmo.ym.module.auth.api.dept.PostApi;
import com.fozmo.ym.module.auth.api.dept.dto.PostRespDTO;
import com.mzt.logapi.service.IParseFunction;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 岗位名字的 {@link IParseFunction} 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PostParseFunction implements IParseFunction {

    public static final String NAME = "getPostById";

    @Resource
    private PostApi postApi;

    @Override
    public String functionName() {
        return NAME;
    }

    @Override
    public String apply(Object value) {
        if (StrUtil.isEmptyIfStr(value)) {
            return "";
        }

        // 获取岗位信息
         PostRespDTO post = postApi.getPostById(Convert.toLong(value));
        if (post == null) {
            log.warn("[apply][获取岗位{{}}为空", value);
            return "";
        }
        return post.getName();
    }

}
