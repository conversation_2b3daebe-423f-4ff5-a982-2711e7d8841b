package com.fozmo.ym.module.log.service.logger;

import com.fozmo.ym.framework.common.pojo.PageResult;
import com.fozmo.ym.module.log.api.logger.dto.OperateLogCreateReqDTO;
import com.fozmo.ym.module.log.api.logger.dto.OperateLogPageReqDTO;
import com.fozmo.ym.module.log.controller.admin.logger.vo.operatelog.OperateLogPageReqVO;
import com.fozmo.ym.module.log.dal.dataobject.logger.OperateLogDO;

/**
 * 操作日志 Service 接口
 *
 *<AUTHOR>
 */
public interface OperateLogService {

    /**
     * 记录操作日志
     *
     * @param createReqDTO 创建请求
     */
    void createOperateLog(OperateLogCreateReqDTO createReqDTO);

    /**
     * 获得操作日志分页列表
     *
     * @param pageReqVO 分页条件
     * @return 操作日志分页列表
     */
    PageResult<OperateLogDO> getOperateLogPage(OperateLogPageReqVO pageReqVO);

    /**
     * 获得操作日志分页列表
     *
     * @param pageReqVO 分页条件
     * @return 操作日志分页列表
     */
    PageResult<OperateLogDO> getOperateLogPage(OperateLogPageReqDTO pageReqVO);

}
