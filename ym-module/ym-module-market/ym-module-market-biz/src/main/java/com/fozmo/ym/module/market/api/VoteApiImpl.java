package com.fozmo.ym.module.market.api;

import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.market.api.dto.VoteBaseInfoDTO;
import com.fozmo.ym.module.market.dal.dataobject.activity.ActivityVoteDO;
import com.fozmo.ym.module.market.service.activity.ActivityVoteService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class VoteApiImpl implements VoteApi {

    @Resource
    private ActivityVoteService voteService;
    /**
     * @param voteId
     * @return
     */
    @Override
    public VoteBaseInfoDTO getVoteBaseInfo(Long voteId) {

        ActivityVoteDO activityVoteDO =voteService.getVoteBaseInfo(voteId);
        return BeanUtils.toBean(activityVoteDO,VoteBaseInfoDTO.class);
    }
}
