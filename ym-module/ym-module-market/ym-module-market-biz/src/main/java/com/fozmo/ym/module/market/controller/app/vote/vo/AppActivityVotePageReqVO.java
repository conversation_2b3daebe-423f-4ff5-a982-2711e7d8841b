package com.fozmo.ym.module.market.controller.app.vote.vo;

import com.fozmo.ym.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "APP - 营销模块-活动类型分页响应对象")
@Accessors(chain = true)
@Data
public class AppActivityVotePageReqVO extends PageParam {
    @Schema(description = "状态 1 进行中 2 已结束 0 未开始  4 手动结束（关闭）")
    private Integer status;

    @Schema(description = "活动类型")
    private String voteType;



}
