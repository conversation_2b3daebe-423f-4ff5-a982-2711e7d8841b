package com.fozmo.ym.module.market.dal.dataobject.activity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fozmo.ym.framework.tenant.core.db.TenantBaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

@TableName(value = "market_activity_vote", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class ActivityVoteDO extends TenantBaseDO {


    @Schema(description = "活动id")
    private Long id;

    @Schema(description = "活动名称")
    private String voteName;

    @Schema(description = "活动描述")
    private String description;

    @Schema(description = "活动状态")
    private Integer status;

    @Schema(description = "活动开始时间")
    private LocalDateTime voteStartTime;

    @Schema(description = "活动结束时间")
    private LocalDateTime voteEndTime;

    @Schema(description = "活动图片")
    private String voteCover;

    @Schema(description = "空间id")
    private Long spaceId;

    @Schema(description = "主办方")
    private String sponsor;

    @Schema(description = "活动类型")
    private String voteType;

    @Schema(description = "投票限制 1每日一票 2 活动期间一票 3 无限制")
    private Integer voteLimit;

    @Schema(description = "活动规则-只做展示不限制")
    private String rule;

    @Schema(description = "账户id")
    private Long accountId;

    /**
     * 活动类型排序
     */
    private Integer sort;

    /**
     * 活动类型图标
     */
    private String icon;

    /**
     * 活动类型背景图
     */
    private String background;

    /**
     * 创建人id
     */
    private Long createId;
    /**
     * 创建日期
     */
    private LocalDate createData;
    /**
     * 更新人
     */
    private Long updaterId;
    /**
     * 更新日期
     */
    private LocalDate updateData;
    /**
     * 租户Code
     */
    private String tenantCode;
}
