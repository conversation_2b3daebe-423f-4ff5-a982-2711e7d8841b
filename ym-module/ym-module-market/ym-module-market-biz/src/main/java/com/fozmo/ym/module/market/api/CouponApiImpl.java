package com.fozmo.ym.module.market.api;

import com.fozmo.ym.framework.common.util.object.BeanUtils;
import com.fozmo.ym.module.market.api.dto.CouponAccountDTO;
import com.fozmo.ym.module.market.dal.dataobject.coupon.CouponDO;
import com.fozmo.ym.module.market.service.coupon.CouponService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CouponApiImpl implements CouponApi {

    @Resource
    private CouponService couponService;

    /**
     * @param goodsType
     * @return
     */
    @Override
    public List<CouponAccountDTO> getCouponList(Long goodsType, Long accountId) {
        List<CouponDO> couponDOList = couponService.getCouponListByGoodsType(goodsType, accountId);
        return BeanUtils.toBean(couponDOList, CouponAccountDTO.class);
    }
}
