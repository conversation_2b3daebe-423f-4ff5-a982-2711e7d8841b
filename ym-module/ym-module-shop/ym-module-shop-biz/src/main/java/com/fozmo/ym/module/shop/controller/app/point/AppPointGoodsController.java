package com.fozmo.ym.module.shop.controller.app.point;

import com.fozmo.ym.framework.apilog.core.annotation.ApiAccessLog;
import com.fozmo.ym.framework.common.pojo.CommonResult;
import com.fozmo.ym.module.shop.controller.app.point.vo.*;
import com.fozmo.ym.module.shop.service.point.PointGoodsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "APP-商城模块-元宝充值")
@RestController
@RequestMapping("/shop/point")
@Validated
public class AppPointGoodsController {

    @Resource
    private PointGoodsService pointGoodsService;

    @GetMapping("/goodsList")
    @Operation(summary = "元宝充值选项列表", description = "商品列表")
    @PermitAll
    @ApiAccessLog
    public CommonResult<List<AppPointGoodsPageResVO>> goodsList() {
        return CommonResult.success(pointGoodsService.goodsList());
    }

    @GetMapping("/goodsInfo")
    @Operation(summary = "元宝充值商品详情", description = "商品详情")
    @PermitAll
    @ApiAccessLog
    public CommonResult<AppPointGoodsPageResVO> goodsInfo(Long goodsId) {
        return CommonResult.success(pointGoodsService.goodsInfo(goodsId));
    }

    @PostMapping("/createOrder")
    @Operation(summary = "元宝充值-创建订单", description = "创建订单")
    @PermitAll
    @ApiAccessLog
    public CommonResult<AppPointOrderResVO> createOrder(@RequestBody AppPointOrderReqVO reqVO) {
        return CommonResult.success(pointGoodsService.createOrder(reqVO));
    }

    @PostMapping("/pay")
    @Operation(summary = "元宝充值-支付", description = "支付订单")
    @PermitAll
    @ApiAccessLog
    public CommonResult<AppPointPayResVO> payOrder(@RequestBody AppPointPayReqVO reqVO) {
        return CommonResult.success(pointGoodsService.payOrder(reqVO));
    }


    @GetMapping("/goodsOrderPage")
    @Operation(summary = "元宝充值-订单分页", description = "订单列表")
    @PermitAll
    @ApiAccessLog
    public CommonResult<List<AppPointOrderPageResVO>> goodsOrderList() {
        return CommonResult.success(pointGoodsService.goodsOrderList());
    }

    @GetMapping("/goodsPointPage")
    @Operation(summary = "元宝充值-积分消费明细分页", description = "积分消费明细")
    @PermitAll
    @ApiAccessLog
    public CommonResult<List<AppPointOrderPageResVO>> goodsPointPage() {
        return CommonResult.success(pointGoodsService.goodsPointPage());
    }


    @GetMapping("/goodsPointInfo")
    @Operation(summary = "元宝充值-积分详情", description = "积分详情")
    @PermitAll
    @ApiAccessLog
    public CommonResult<List<AppPointInfoResVO>> goodsPointInfo() {
        return CommonResult.success(pointGoodsService.goodsPointInfo());
    }

}
