package com.fozmo.ym.module.shop.controller.app.point.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "元宝详情信息")
public class AppPointInfoResVO {

    @Schema(description = "id 钱包编号")
    private Long id;

    @Schema(description = "用户id")
    private Long accountId;

    @Schema(description = "元宝数量")
    private Integer point;

    @Schema(description = "单位")
    private String unit;


}
