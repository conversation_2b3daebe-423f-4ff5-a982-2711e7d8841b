package com.fozmo.ym.module.sms.dto.code;

import com.fozmo.ym.framework.common.validation.InEnum;
import com.fozmo.ym.framework.common.validation.Mobile;
import com.fozmo.ym.module.sms.enums.SmsSceneEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

/**
 * 短信验证码的校验 Request DTO
 *
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class SmsCodeValidateReqDTO {

    /**
     * 手机号
     */
    @Mobile
    @NotEmpty(message = "手机号不能为空")
    private String mobile;
    /**
     * 发送场景
     */
    @NotNull(message = "发送场景不能为空")
    @InEnum(SmsSceneEnum.class)
    private Integer scene;
    /**
     * 验证码
     */
    @NotEmpty(message = "验证码")
    private String code;

}
