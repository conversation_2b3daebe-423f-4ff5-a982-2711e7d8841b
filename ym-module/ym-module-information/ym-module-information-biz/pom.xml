<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fozmo</groupId>
        <artifactId>ym-module-information</artifactId>
        <version>1.0</version>
    </parent>

    <artifactId>ym-module-information-biz</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <!-- 消息服务模块api-->
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-information-api</artifactId>
            <version>1.0</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.fozmo</groupId>-->
<!--            <artifactId>ym-spring-boot-starter-websocket</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-mq</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
    </dependencies>

</project>