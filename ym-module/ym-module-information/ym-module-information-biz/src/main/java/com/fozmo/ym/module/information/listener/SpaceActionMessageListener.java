package com.fozmo.ym.module.information.listener;


import com.fozmo.ym.framework.common.enums.UserTypeEnum;
import com.fozmo.ym.module.information.core.listener.SpaceWebSocketMessageListener;
import com.fozmo.ym.module.information.core.sender.redis.SpaceRedisWebSocketMessageSender;
import com.fozmo.ym.module.information.core.utils.WebSocketFrameworkUtils;
import com.fozmo.ym.module.information.message.SpaceActionMessage;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketSession;

@Component
public class SpaceActionMessageListener implements SpaceWebSocketMessageListener<SpaceActionMessage> {

    @Resource
    private SpaceRedisWebSocketMessageSender spaceRedisWebSocketMessageSender;

    @Override
    public void onMessage(WebSocketSession session, SpaceActionMessage message) {

        message.setUserId(WebSocketFrameworkUtils.getLoginUserId(session)+"");
        String spaceId = message.getSpaceId();
        if (spaceId == null) {
            return;
        }
        spaceRedisWebSocketMessageSender.sendObject(spaceId, UserTypeEnum.MEMBER.getValue(), "action-message-"+spaceId, message );

    }

    @Override
    public String getType() {
        return "action-message";
    }
}
