package com.fozmo.ym.module.information.config;

import com.fozmo.ym.framework.mq.redis.config.YmRedisMQConsumerAutoConfiguration;
import com.fozmo.ym.framework.mq.redis.core.RedisMQTemplate;
import com.fozmo.ym.module.information.core.handler.SpaceWebSocketMessageHandler;
import com.fozmo.ym.module.information.core.listener.SpaceWebSocketMessageListener;
import com.fozmo.ym.module.information.core.security.SpaceLoginUserHandshakeInterceptor;
import com.fozmo.ym.module.information.core.security.SpaceWebSocketAuthorizeRequestsCustomizer;
import com.fozmo.ym.module.information.core.sender.redis.SpaceRedisWebSocketMessageConsumer;
import com.fozmo.ym.module.information.core.sender.redis.SpaceRedisWebSocketMessageSender;
import com.fozmo.ym.module.information.core.session.SpaceWebSocketSessionHandlerDecorator;
import com.fozmo.ym.module.information.core.session.SpaceWebSocketSessionManager;
import com.fozmo.ym.module.information.core.session.SpaceWebSocketSessionManagerImpl;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.util.List;

/**
 * WebSocket 自动配置
 *
 * <AUTHOR>
 */
@AutoConfiguration(before = YmRedisMQConsumerAutoConfiguration.class) // before YmRedisMQConsumerAutoConfiguration 的原因是，需要保证 RedisWebSocketMessageConsumer 先创建，才能创建 RedisMessageListenerContainer
@EnableWebSocket // 开启 websocket
@ConditionalOnProperty(prefix = "ym.space.websocket", value = "enable", matchIfMissing = true) // 允许使用 ym.websocket.enable=false 禁用 websocket
@EnableConfigurationProperties(SpaceWebSocketProperties.class)
public class SpaceWebSocketAutoConfiguration {

    @Bean(name = "spaceWebSocketConfigurer")
    public WebSocketConfigurer spaceWebSocketConfigurer(HandshakeInterceptor[] handshakeInterceptors,
                                                        WebSocketHandler webSocketHandler,
                                                        SpaceWebSocketProperties webSocketProperties) {
        return registry -> registry
                // 添加 WebSocketHandler
                .addHandler(webSocketHandler, webSocketProperties.getPath())
                .addInterceptors(handshakeInterceptors)
                // 允许跨域，否则前端连接会直接断开
                .setAllowedOriginPatterns("*");
    }

    @Bean(name ="spaceHandshakeInterceptor" )
    public HandshakeInterceptor handshakeInterceptor() {
        return new SpaceLoginUserHandshakeInterceptor();
    }

    @Bean(name = "spaceWebSocketHandler")
    public WebSocketHandler webSocketHandler(SpaceWebSocketSessionManager spaceWebSocketSessionManager,
                                             List<? extends SpaceWebSocketMessageListener<?>> messageListeners) {
        // 1. 创建 JsonWebSocketMessageHandler 对象，处理消息
        SpaceWebSocketMessageHandler messageHandler = new SpaceWebSocketMessageHandler(messageListeners);
        // 2. 创建 WebSocketSessionHandlerDecorator 对象，处理连接
        return new SpaceWebSocketSessionHandlerDecorator(messageHandler, spaceWebSocketSessionManager);
    }

    @Bean
    public SpaceWebSocketSessionManager spaceWebSocketSessionManager() {
        return new SpaceWebSocketSessionManagerImpl();
    }

    @Bean
    public SpaceWebSocketAuthorizeRequestsCustomizer spaceWebSocketAuthorizeRequestsCustomizer(SpaceWebSocketProperties spaceWebSocketProperties) {
        return new SpaceWebSocketAuthorizeRequestsCustomizer(spaceWebSocketProperties);
    }

    @Configuration
    @ConditionalOnProperty(prefix = "ym.space.websocket", name = "sender-type", havingValue = "redis")
    public class RedisWebSocketMessageSenderConfiguration {

        @Bean
        public SpaceRedisWebSocketMessageSender spaceRedisWebSocketMessageSender(SpaceWebSocketSessionManager spaceWebSocketSessionManager, RedisMQTemplate redisMQTemplate) {
            return new SpaceRedisWebSocketMessageSender(spaceWebSocketSessionManager, redisMQTemplate);
        }

        @Bean
        public SpaceRedisWebSocketMessageConsumer spaceRedisWebSocketMessageConsumer(
                SpaceRedisWebSocketMessageSender spaceRedisWebSocketMessageSender) {
            return new SpaceRedisWebSocketMessageConsumer(spaceRedisWebSocketMessageSender);
        }

    }

}