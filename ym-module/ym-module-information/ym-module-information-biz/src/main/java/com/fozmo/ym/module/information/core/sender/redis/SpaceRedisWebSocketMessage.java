package com.fozmo.ym.module.information.core.sender.redis;

import com.fozmo.ym.framework.mq.redis.core.pubsub.AbstractRedisChannelMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * Redis 广播 WebSocket 的消息
 */
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
public class SpaceRedisWebSocketMessage extends AbstractRedisChannelMessage {

    /**
     * Session 编号
     */
    private String sessionId;
    /**
     * spaceId 空间编号
     */
    private String spaceId;
    /**
     * 用户类型
     */
    private Integer userType;
    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 消息类型
     */
    private String messageType;
    /**
     * 消息内容
     */
    private String messageContent;

}
