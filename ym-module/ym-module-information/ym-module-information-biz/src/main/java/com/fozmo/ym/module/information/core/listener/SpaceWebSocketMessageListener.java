package com.fozmo.ym.module.information.core.listener;

import com.fozmo.ym.module.information.core.message.SpaceWebSocketMessage;
import org.springframework.web.socket.WebSocketSession;

/**
 * WebSocket 消息监听器接口
 *
 * 目的：前端发送消息给后端后，处理对应 {@link #getType()} 类型的消息
 *
 * @param <T> 泛型，消息类型
 */
public interface SpaceWebSocketMessageListener<T> {

    /**
     * 处理消息
     *
     * @param session Session
     * @param message 消息
     */
    void onMessage(WebSocketSession session, T message);

    /**
     * 获得消息类型
     *
     * @see SpaceWebSocketMessage#getType()
     * @return 消息类型
     */
    String getType();

}
