package com.fozmo.ym.module.information.core.message;

import com.fozmo.ym.module.information.core.listener.SpaceWebSocketMessageListener;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * JSON 格式的 WebSocket 消息帧
 *
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class SpaceWebSocketMessage implements Serializable {

    /**
     * 消息类型
     *
     * 目的：用于分发到对应的 {@link SpaceWebSocketMessageListener} 实现类
     */
    private String type;
    /**
     * 消息内容
     *
     * 要求 JSON 对象
     */
    private String content;

}
