package com.fozmo.ym.module.information.core.sender.redis;

import com.fozmo.ym.framework.mq.redis.core.RedisMQTemplate;
import com.fozmo.ym.module.information.core.sender.AbstractSpaceWebSocketMessageSender;
import com.fozmo.ym.module.information.core.sender.SpaceWebSocketMessageSender;
import com.fozmo.ym.module.information.core.session.SpaceWebSocketSessionManager;
import lombok.extern.slf4j.Slf4j;

/**
 * 基于 Redis 的 {@link SpaceWebSocketMessageSender} 实现类
 *
 * <AUTHOR>
 */
@Slf4j
public class SpaceRedisWebSocketMessageSender extends AbstractSpaceWebSocketMessageSender {

    private final RedisMQTemplate redisMQTemplate;

    public SpaceRedisWebSocketMessageSender(SpaceWebSocketSessionManager sessionManager,
                                            RedisMQTemplate redisMQTemplate) {
        super(sessionManager);
        this.redisMQTemplate = redisMQTemplate;
    }

    @Override
    public void send(String spaceId,Integer userType, String messageType, String messageContent) {
        sendRedisMessage(spaceId,null, null, userType, messageType, messageContent);
    }

    @Override
    public void sendObject(String spaceId, Integer userType, Long userId, String messageType, Object messageContent) {
        super.sendObject(spaceId, userType, userId, messageType, messageContent);
    }

    @Override
    public void sendObject(String spaceId,Integer userType, String messageType, Object messageContent) {
        super.sendObject(spaceId,userType, messageType, messageContent);
    }

    @Override
    public void sendObject(String spaceId,String sessionId, String messageType, Object messageContent) {
        super.sendObject(spaceId,sessionId, messageType, messageContent);
    }

    @Override
    public void send(String spaceId,String sessionId, String messageType, String messageContent) {
        sendRedisMessage(spaceId,sessionId, null, null, messageType, messageContent);
    }

    /**
     * 通过 Redis 广播消息
     *
     * @param sessionId Session 编号
     * @param userId 用户编号
     * @param userType 用户类型
     * @param messageType 消息类型
     * @param messageContent 消息内容
     */
    private void sendRedisMessage(String spaceId,String sessionId, Long userId, Integer userType,
                                  String messageType, String messageContent) {
        SpaceRedisWebSocketMessage mqMessage = new SpaceRedisWebSocketMessage();
        mqMessage.setSessionId(sessionId);mqMessage.setUserId(userId);mqMessage.setUserType(userType);mqMessage.setSpaceId(spaceId);
        mqMessage.setMessageType(messageType);mqMessage.setMessageContent(messageContent);
        redisMQTemplate.send(mqMessage);
    }

}
