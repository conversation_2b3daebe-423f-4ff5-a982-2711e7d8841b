package com.fozmo.ym.module.information.core.security;

import com.fozmo.ym.framework.security.config.AuthorizeRequestsCustomizer;
import com.fozmo.ym.module.information.config.SpaceWebSocketProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer;

/**
 * WebSocket 的权限自定义
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class SpaceWebSocketAuthorizeRequestsCustomizer extends AuthorizeRequestsCustomizer {

    private final SpaceWebSocketProperties webSocketProperties;

    @Override
    public void customize(AuthorizeHttpRequestsConfigurer<HttpSecurity>.AuthorizationManagerRequestMatcherRegistry registry) {
        registry.requestMatchers(webSocketProperties.getPath()).permitAll();
    }

}
