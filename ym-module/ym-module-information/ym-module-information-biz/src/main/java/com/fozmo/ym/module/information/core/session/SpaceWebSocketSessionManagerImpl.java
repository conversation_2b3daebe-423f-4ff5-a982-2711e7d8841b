package com.fozmo.ym.module.information.core.session;

import cn.hutool.core.collection.CollUtil;
import com.fozmo.ym.framework.security.core.LoginUser;
import com.fozmo.ym.framework.tenant.core.context.TenantContextHolder;
import com.fozmo.ym.module.information.core.utils.WebSocketFrameworkUtils;
import com.fozmo.ym.module.information.listener.SpaceBroadcastMessageListener;
import com.fozmo.ym.module.information.message.SpaceBroadcastMessage;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CopyOnWriteArrayList;

@Service
public class SpaceWebSocketSessionManagerImpl implements SpaceWebSocketSessionManager {

    private final ConcurrentMap<String, WebSocketSession> idSessions = new ConcurrentHashMap<>();
    private final ConcurrentMap<Integer, ConcurrentMap<Long, CopyOnWriteArrayList<WebSocketSession>>> userSessions = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, ConcurrentMap<String, CopyOnWriteArrayList<WebSocketSession>>> spaceSessions = new ConcurrentHashMap<>();
    @Resource
    private SpaceBroadcastMessageListener spaceBroadcastMessageListener;

    @Override
    public void addSession(WebSocketSession session) {
        // 添加到idSessions
        idSessions.put(session.getId(), session);

        LoginUser user = WebSocketFrameworkUtils.getLoginUser(session);
        if (user == null) {
            return;
        }

        // 添加到spaceSessions（优化后的线程安全实现）
        if (user.getSpaceId() != null) {
            spaceSessions.computeIfAbsent(user.getSpaceId(), k -> new ConcurrentHashMap<>())
                    .computeIfAbsent(user.getId().toString(), k -> new CopyOnWriteArrayList<>())
                    .add(session);
            sendBroadcastMessage(user,session,"0");
        }

        // 添加到userSessions（优化后的线程安全实现）
        userSessions.computeIfAbsent(user.getUserType(), k -> new ConcurrentHashMap<>())
                .computeIfAbsent(user.getId(), k -> new CopyOnWriteArrayList<>())
                .add(session);
    }

    @Override
    public void removeSession(WebSocketSession session) {
        // 从idSessions移除
        idSessions.remove(session.getId());

        LoginUser user = WebSocketFrameworkUtils.getLoginUser(session);
        if (user == null) {
            return;
        }

        // 从userSessions移除（优化后的线程安全实现）
        Optional.ofNullable(userSessions.get(user.getUserType()))
                .map(map -> map.get(user.getId()))
                .ifPresent(sessions -> {
                    sessions.removeIf(s -> s.getId().equals(session.getId()));
                    if (sessions.isEmpty()) {
                        userSessions.get(user.getUserType()).remove(user.getId());
                    }
                });

        // 从spaceSessions移除（如果存在）
        if (user.getSpaceId() != null) {
            Optional.ofNullable(spaceSessions.get(user.getSpaceId()))
                    .map(map -> map.get(user.getId().toString()))
                    .ifPresent(sessions -> {
                        sessions.removeIf(s -> s.getId().equals(session.getId()));
                        if (sessions.isEmpty()) {
                            spaceSessions.get(user.getSpaceId()).remove(user.getId().toString());
                        }
                    });

            sendBroadcastMessage(user,session,"1");
        }
    }

    @Override
    public WebSocketSession getSession(String id) {
        return idSessions.get(id);
    }

    @Override
    public Collection<WebSocketSession> getSessionList(Integer userType) {
        return Optional.ofNullable(userSessions.get(userType))
                .map(userMap -> {
                    Collection<WebSocketSession> result = new ArrayList<>();
                    Long contextTenantId = TenantContextHolder.getTenantId();

                    userMap.values().forEach(sessions -> {
                        if (CollUtil.isNotEmpty(sessions)) {
                            if (contextTenantId == null ||
                                    contextTenantId.equals(WebSocketFrameworkUtils.getTenantId(sessions.get(0)))) {
                                result.addAll(sessions);
                            }
                        }
                    });
                    return result;
                })
                .orElse(Collections.emptyList());
    }

    @Override
    public Collection<WebSocketSession> getSessionList(Integer userType, Long userId) {
        return Optional.ofNullable(userSessions.get(userType))
                .map(map -> map.get(userId))
                .map(ArrayList::new)
                .orElse(new ArrayList<>());
    }

    @Override
    public Collection<WebSocketSession> getSessionListBySpaceId(String spaceId) {
        return Optional.ofNullable(spaceSessions.get(spaceId))
                .map(spaceMap -> {
                    Collection<WebSocketSession> result = new ArrayList<>();
                    spaceMap.values().forEach(result::addAll);
                    return result;
                })
                .orElse(Collections.emptyList());
    }

    /**
     * 根据空间ID和用户ID获取WebSocket会话
     */
    @Override
    public Collection<WebSocketSession> getSessionListBySpaceAndUser(String spaceId, Long userId) {
        return Optional.ofNullable(spaceSessions.get(spaceId))
                .map(spaceMap -> spaceMap.get(String.valueOf(userId)))
                .map(ArrayList::new)
                .orElse(new ArrayList<>());
    }


    private void sendBroadcastMessage(LoginUser user,WebSocketSession session,String type) {
        SpaceBroadcastMessage message = new SpaceBroadcastMessage();
        message.setSpaceId(user.getSpaceId());
        message.setUserId(user.getId()+"");
        message.setUserName(user.getUsername());
        if (type != null) {
            message.setType(type);
            if ("0".equals(type)) {
                message.setTypeName("进入空间");
            }else {
                message.setTypeName("离开空间");
            }
        }
        spaceBroadcastMessageListener.onMessage(session,message);
    }
}
