package com.fozmo.ym.module.information.core.sender.redis;

import com.fozmo.ym.framework.mq.redis.core.pubsub.AbstractRedisChannelMessageListener;
import lombok.RequiredArgsConstructor;

/**
 * {@link SpaceRedisWebSocketMessage} 广播消息的消费者，真正把消息发送出去
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class SpaceRedisWebSocketMessageConsumer extends AbstractRedisChannelMessageListener<SpaceRedisWebSocketMessage> {

    private final SpaceRedisWebSocketMessageSender redisWebSocketMessageSender;

    @Override
    public void onMessage(SpaceRedisWebSocketMessage message) {
        redisWebSocketMessageSender.send(message.getSpaceId(),message.getSessionId(),
                message.getUserType(), message.getUserId(),
                message.getMessageType(), message.getMessageContent());
    }

}
