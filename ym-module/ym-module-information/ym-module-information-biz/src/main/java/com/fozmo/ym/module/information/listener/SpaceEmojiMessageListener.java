package com.fozmo.ym.module.information.listener;

import com.fozmo.ym.framework.common.enums.UserTypeEnum;
import com.fozmo.ym.module.information.core.listener.SpaceWebSocketMessageListener;
import com.fozmo.ym.module.information.core.sender.redis.SpaceRedisWebSocketMessageSender;
import com.fozmo.ym.module.information.core.utils.WebSocketFrameworkUtils;
import com.fozmo.ym.module.information.message.SpaceEmojiMessage;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketSession;

@Component
public class SpaceEmojiMessageListener implements SpaceWebSocketMessageListener<SpaceEmojiMessage> {
    @Resource
    private SpaceRedisWebSocketMessageSender spaceRedisWebSocketMessageSender;

    @Override
    public void onMessage(WebSocketSession session, SpaceEmojiMessage message) {

        message.setUserId(WebSocketFrameworkUtils.getLoginUserId(session)+"");
        String spaceId = message.getSpaceId();
        if (spaceId == null) {
            return;
        }
        // 群发
        spaceRedisWebSocketMessageSender.sendObject(spaceId, UserTypeEnum.MEMBER.getValue(), "emoji-message-"+spaceId, message );
    }

    @Override
    public String getType() {
        return "emoji-message";
    }
}
