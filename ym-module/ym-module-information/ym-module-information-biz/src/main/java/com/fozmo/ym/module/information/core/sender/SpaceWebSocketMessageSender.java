package com.fozmo.ym.module.information.core.sender;

import com.fozmo.ym.framework.common.util.json.JsonUtils;

/**
 * WebSocket 消息的发送器接口
 *
 * <AUTHOR>
 */
public interface SpaceWebSocketMessageSender {

    /**
     * 发送消息给指定用户
     *
     * @param userType 用户类型
     * @param userId 用户编号
     * @param messageType 消息类型
     * @param messageContent 消息内容，JSON 格式
     */
    void send(String spaceId,Integer userType, Long userId, String messageType, String messageContent);

    /**
     * 发送消息给指定用户类型
     *
     * @param userType 用户类型
     * @param messageType 消息类型
     * @param messageContent 消息内容，JSON 格式
     */
    void send(String spaceId,Integer userType, String messageType, String messageContent);

    /**
     * 发送消息给指定 Session
     *
     * @param sessionId Session 编号
     * @param messageType 消息类型
     * @param messageContent 消息内容，JSON 格式
     */
    void send(String spaceId,String sessionId, String messageType, String messageContent);

    default void sendObject(String spaceId,Integer userType, Long userId, String messageType, Object messageContent) {
        send(spaceId,userType, userId, messageType, JsonUtils.toJsonString(messageContent));
    }

    default void sendObject(String spaceId,Integer userType, String messageType, Object messageContent) {
        send(spaceId,userType, messageType, JsonUtils.toJsonString(messageContent));
    }

    default void sendObject(String spaceId,String sessionId, String messageType, Object messageContent) {
        send(spaceId,sessionId, messageType, JsonUtils.toJsonString(messageContent));
    }

}
