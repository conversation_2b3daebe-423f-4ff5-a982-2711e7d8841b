2025-09-09 17:22:45.009 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:22:45.012 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:22:45.013 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:22:45.013 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:22:45.013 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:22:45.013 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:22:45.013 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:22:45.014 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:22:45.014 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:22:45.014 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:22:45.014 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:22:45.015 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:22:45.015 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:22:53.973 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [77aa1002] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:22:53.974 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:22:53.974 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:22:53.974 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:22:54.003 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:22:54.003 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@2d0f7f17]
2025-09-09 17:22:54.003 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:22:54.004 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [77aa1002] [3dca211a-17, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:22:54.004 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [77aa1002] [3dca211a-17, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:22:54.997 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:22:55.010 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:22:55.011 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:22:55.011 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:22:55.011 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:22:55.011 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:22:55.011 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:22:55.012 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:22:55.012 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:22:55.012 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:22:55.012 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:22:55.012 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:22:55.012 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:23:05.001 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:23:05.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:23:05.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:23:05.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:23:05.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:23:05.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:23:05.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:23:05.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:23:05.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:23:05.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:23:05.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:23:05.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:23:05.021 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:23:13.968 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [6204b57c] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:23:13.970 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:23:13.970 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:23:13.970 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:23:13.990 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:23:13.990 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@53dc60aa]
2025-09-09 17:23:13.990 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:23:13.990 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [6204b57c] [3dca211a-18, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:23:13.991 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [6204b57c] [3dca211a-18, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:23:15.005 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:23:15.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:23:15.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:23:15.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:23:15.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:23:15.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:23:15.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:23:15.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:23:15.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:23:15.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:23:15.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:23:15.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:23:15.024 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:23:16.025 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connection refused: no further information

java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:23:25.012 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:23:25.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:23:25.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:23:25.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:23:25.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:23:25.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:23:25.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:23:25.032 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:23:25.032 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:23:25.032 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:23:25.032 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:23:25.033 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:23:25.033 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:23:33.979 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [59ec22fb] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:23:33.980 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:23:33.980 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:23:33.980 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:23:34.013 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:23:34.013 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@7907393b]
2025-09-09 17:23:34.013 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:23:34.014 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [59ec22fb] [3dca211a-19, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:23:34.014 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [59ec22fb] [3dca211a-19, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:23:35.004 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:23:35.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:23:35.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:23:35.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:23:35.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:23:35.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:23:35.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:23:35.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:23:35.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:23:35.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:23:35.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:23:35.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:23:35.026 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:23:45.000 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:23:45.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:23:45.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:23:45.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:23:45.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:23:45.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:23:45.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:23:45.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:23:45.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:23:45.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:23:45.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:23:45.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:23:45.020 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:23:48.149 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connection refused: no further information

java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:23:53.968 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2051e9cd] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:23:53.969 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:23:53.969 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:23:53.969 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:23:53.991 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:23:53.991 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@79214052]
2025-09-09 17:23:53.991 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:23:53.991 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2051e9cd] [3dca211a-20, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:23:53.991 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [2051e9cd] [3dca211a-20, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:23:55.004 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:23:55.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:23:55.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:23:55.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:23:55.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:23:55.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:23:55.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:23:55.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:23:55.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:23:55.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:23:55.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:23:55.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:23:55.024 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:24:05.002 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:24:05.005 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:24:05.006 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:24:05.006 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:24:05.006 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:24:05.006 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:24:05.007 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:24:05.009 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:24:05.009 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:24:05.009 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:24:05.009 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:24:05.009 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:24:05.010 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:24:13.969 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [593dd6a0] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:24:13.970 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:24:13.970 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:24:13.970 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:24:14.087 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:24:14.087 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@16671a8b]
2025-09-09 17:24:14.087 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:24:14.087 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [593dd6a0] [3dca211a-21, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:24:14.088 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [593dd6a0] [3dca211a-21, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:24:15.005 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:24:15.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:24:15.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:24:15.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:24:15.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:24:15.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:24:15.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:24:15.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:24:15.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:24:15.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:24:15.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:24:15.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:24:15.026 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:24:20.322 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connection refused: no further information

java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:24:23.980 | [39mDEBUG 13944[0;39m | [1;33mparallel-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [11962910] HTTP GET http://127.0.0.1:11000/actuator/info
2025-09-09 17:24:23.982 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/info", parameters={}
2025-09-09 17:24:23.982 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'info'
2025-09-09 17:24:23.982 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:24:23.982 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:24:23.982 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Writing [{}]
2025-09-09 17:24:23.982 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:24:23.982 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [11962910] [3dca211a-22, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:24:23.983 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [11962910] [3dca211a-22, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{}]
2025-09-09 17:24:25.003 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:24:25.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:24:25.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:24:25.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:24:25.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:24:25.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:24:25.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:24:25.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:24:25.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:24:25.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:24:25.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:24:25.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:24:25.022 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:24:33.981 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [75ca3f81] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:24:33.982 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:24:33.982 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:24:33.983 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:24:34.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:24:34.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@6051c229]
2025-09-09 17:24:34.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:24:34.020 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [75ca3f81] [3dca211a-23, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:24:34.020 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [75ca3f81] [3dca211a-23, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:24:35.003 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:24:35.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:24:35.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:24:35.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:24:35.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:24:35.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:24:35.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:24:35.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:24:35.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:24:35.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:24:35.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:24:35.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:24:35.025 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:24:45.004 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:24:45.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:24:45.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:24:45.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:24:45.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:24:45.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:24:45.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:24:45.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:24:45.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:24:45.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:24:45.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:24:45.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:24:45.024 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:24:52.450 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connection refused: no further information

java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:24:53.980 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2b097ff6] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:24:53.981 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:24:53.982 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:24:53.982 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:24:54.004 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:24:54.004 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@25d30c56]
2025-09-09 17:24:54.004 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:24:54.004 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2b097ff6] [3dca211a-24, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:24:54.004 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [2b097ff6] [3dca211a-24, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:24:55.004 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:24:55.007 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:24:55.008 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:24:55.008 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:24:55.008 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:24:55.008 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:24:55.008 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:24:55.009 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:24:55.009 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:24:55.009 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:24:55.009 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:24:55.010 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:24:55.010 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:25:05.004 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:25:05.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:25:05.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:25:05.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:25:05.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:25:05.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:25:05.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:25:05.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:25:05.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:25:05.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:25:05.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:25:05.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:25:05.023 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:25:13.978 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [22756731] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:25:13.979 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:25:13.979 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:25:13.980 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:25:14.000 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:25:14.001 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@62159387]
2025-09-09 17:25:14.001 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:25:14.001 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [22756731] [3dca211a-25, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:25:14.001 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [22756731] [3dca211a-25, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:25:15.011 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:25:15.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:25:15.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:25:15.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:25:15.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:25:15.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:25:15.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:25:15.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:25:15.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:25:15.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:25:15.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:25:15.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:25:15.031 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:25:23.988 | [39mDEBUG 13944[0;39m | [1;33mparallel-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [192c288a] HTTP GET http://127.0.0.1:11000/actuator/info
2025-09-09 17:25:23.989 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/info", parameters={}
2025-09-09 17:25:23.990 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'info'
2025-09-09 17:25:23.990 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:25:23.990 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:25:23.990 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Writing [{}]
2025-09-09 17:25:23.990 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:25:23.990 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [192c288a] [3dca211a-26, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:25:23.991 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [192c288a] [3dca211a-26, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{}]
2025-09-09 17:25:24.573 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connection refused: no further information

java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:25:25.005 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:25:25.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:25:25.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:25:25.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:25:25.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:25:25.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:25:25.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:25:25.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:25:25.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:25:25.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:25:25.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:25:25.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:25:25.027 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:25:33.979 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [37ad8c82] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:25:33.980 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:25:33.980 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:25:33.982 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:25:34.002 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:25:34.002 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@7399b2ac]
2025-09-09 17:25:34.002 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:25:34.002 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [37ad8c82] [3dca211a-27, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:25:34.002 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [37ad8c82] [3dca211a-27, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:25:35.001 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:25:35.005 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:25:35.005 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:25:35.005 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:25:35.005 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:25:35.006 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:25:35.006 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:25:35.006 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:25:35.006 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:25:35.006 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:25:35.006 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:25:35.006 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:25:35.007 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:25:45.001 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:25:45.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:25:45.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:25:45.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:25:45.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:25:45.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:25:45.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:25:45.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:25:45.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:25:45.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:25:45.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:25:45.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:25:45.024 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:25:53.978 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [5c324084] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:25:53.979 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:25:53.980 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:25:53.980 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:25:54.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:25:54.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@244c8d35]
2025-09-09 17:25:54.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:25:54.030 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [5c324084] [3dca211a-28, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:25:54.030 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [5c324084] [3dca211a-28, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:25:55.006 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:25:55.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:25:55.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:25:55.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:25:55.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:25:55.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:25:55.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:25:55.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:25:55.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:25:55.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:25:55.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:25:55.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:25:55.026 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:25:56.740 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connection refused: no further information

java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:26:05.002 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:26:05.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:26:05.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:26:05.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:26:05.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:26:05.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:26:05.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:26:05.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:26:05.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:26:05.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:26:05.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:26:05.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:26:05.021 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:26:13.981 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [7fbb8c57] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:26:13.983 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:26:13.983 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:26:13.983 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:26:14.004 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:26:14.004 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@14c31cd]
2025-09-09 17:26:14.004 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:26:14.004 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [7fbb8c57] [3dca211a-29, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:26:14.004 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [7fbb8c57] [3dca211a-29, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:26:15.000 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:26:15.004 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:26:15.005 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:26:15.005 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:26:15.005 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:26:15.005 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:26:15.005 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:26:15.006 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:26:15.006 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:26:15.006 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:26:15.006 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:26:15.008 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:26:15.008 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:26:25.007 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:26:25.010 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:26:25.011 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:26:25.011 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:26:25.011 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:26:25.011 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:26:25.011 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:26:25.012 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:26:25.012 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:26:25.012 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:26:25.012 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:26:25.012 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:26:25.012 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:26:28.819 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connection refused: no further information

java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:26:33.969 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [48f7e78] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:26:33.970 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:26:33.970 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:26:33.970 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:26:34.045 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:26:34.045 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@28c51ac5]
2025-09-09 17:26:34.045 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:26:34.045 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [48f7e78] [3dca211a-30, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:26:34.045 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [48f7e78] [3dca211a-30, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:26:34.998 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:26:35.000 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:26:35.001 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:26:35.001 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:26:35.001 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:26:35.001 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:26:35.001 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:26:35.002 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:26:35.002 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:26:35.002 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:26:35.002 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:26:35.002 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:26:35.002 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:26:45.010 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:26:45.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:26:45.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:26:45.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:26:45.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:26:45.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:26:45.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:26:45.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:26:45.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:26:45.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:26:45.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:26:45.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:26:45.031 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:26:53.976 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [26e89f34] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:26:53.977 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:26:53.977 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:26:53.978 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:26:54.284 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:26:54.284 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@2603904f]
2025-09-09 17:26:54.285 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:26:54.285 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [26e89f34] [3dca211a-31, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:26:54.285 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [26e89f34] [3dca211a-31, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:26:55.011 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:26:55.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:26:55.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:26:55.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:26:55.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:26:55.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:26:55.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:26:55.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:26:55.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:26:55.032 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:26:55.032 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:26:55.032 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:26:55.032 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:27:01.008 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connection refused: no further information

java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:27:05.008 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:27:05.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:27:05.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:27:05.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:27:05.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:27:05.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:27:05.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:27:05.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:27:05.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:27:05.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:27:05.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:27:05.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:27:05.027 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:27:13.972 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [51315864] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:27:13.973 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:27:13.973 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:27:13.973 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:27:13.996 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:27:13.996 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@452b31e7]
2025-09-09 17:27:13.996 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:27:13.996 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [51315864] [3dca211a-32, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:27:13.997 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [51315864] [3dca211a-32, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:27:15.012 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:27:15.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:27:15.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:27:15.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:27:15.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:27:15.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:27:15.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:27:15.032 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:27:15.033 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:27:15.033 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:27:15.033 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:27:15.033 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:27:15.033 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:27:23.980 | [39mDEBUG 13944[0;39m | [1;33mparallel-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [26754c8e] HTTP GET http://127.0.0.1:11000/actuator/info
2025-09-09 17:27:23.983 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/info", parameters={}
2025-09-09 17:27:23.983 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'info'
2025-09-09 17:27:23.983 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:27:23.983 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:27:23.983 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Writing [{}]
2025-09-09 17:27:23.983 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:27:23.983 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [26754c8e] [3dca211a-33, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:27:23.983 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [26754c8e] [3dca211a-33, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{}]
2025-09-09 17:27:25.006 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:27:25.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:27:25.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:27:25.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:27:25.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:27:25.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:27:25.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:27:25.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:27:25.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:27:25.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:27:25.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:27:25.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:27:25.024 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:27:33.108 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connection refused: no further information

java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:27:33.972 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [4ca9eec3] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:27:33.973 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:27:33.973 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:27:33.973 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:27:33.997 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:27:33.998 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@762b39c0]
2025-09-09 17:27:33.998 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:27:33.998 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [4ca9eec3] [3dca211a-34, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:27:33.998 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [4ca9eec3] [3dca211a-34, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:27:34.999 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:27:35.015 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:27:35.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:27:35.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:27:35.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:27:35.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:27:35.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:27:35.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:27:35.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:27:35.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:27:35.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:27:35.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:27:35.019 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:27:45.003 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:27:45.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:27:45.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:27:45.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:27:45.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:27:45.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:27:45.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:27:45.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:27:45.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:27:45.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:27:45.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:27:45.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:27:45.022 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:27:53.973 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [1dcc8e24] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:27:53.973 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:27:53.973 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:27:53.973 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:27:53.989 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:27:53.990 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@eec9f6a]
2025-09-09 17:27:53.990 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:27:53.990 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [1dcc8e24] [3dca211a-35, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:27:53.990 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [1dcc8e24] [3dca211a-35, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:27:55.000 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:27:55.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:27:55.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:27:55.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:27:55.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:27:55.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:27:55.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:27:55.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:27:55.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:27:55.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:27:55.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:27:55.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:27:55.020 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:28:05.007 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:28:05.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:28:05.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:28:05.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:28:05.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:28:05.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:28:05.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:28:05.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:28:05.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:28:05.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:28:05.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:28:05.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:28:05.027 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:28:05.169 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connection refused: no further information

java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:28:13.968 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [13c91a8] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:28:13.969 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:28:13.969 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:28:13.969 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:28:13.990 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:28:13.990 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@89849de]
2025-09-09 17:28:13.990 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:28:13.991 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [13c91a8] [3dca211a-36, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:28:13.991 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [13c91a8] [3dca211a-36, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:28:15.000 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:28:15.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:28:15.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:28:15.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:28:15.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:28:15.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:28:15.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:28:15.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:28:15.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:28:15.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:28:15.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:28:15.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:28:15.019 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:28:23.988 | [39mDEBUG 13944[0;39m | [1;33mparallel-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [533be639] HTTP GET http://127.0.0.1:11000/actuator/info
2025-09-09 17:28:23.989 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/info", parameters={}
2025-09-09 17:28:23.989 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'info'
2025-09-09 17:28:23.989 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:28:23.989 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:28:23.989 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Writing [{}]
2025-09-09 17:28:23.989 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:28:23.989 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [533be639] [3dca211a-37, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:28:23.989 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [533be639] [3dca211a-37, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{}]
2025-09-09 17:28:25.009 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:28:25.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:28:25.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:28:25.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:28:25.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:28:25.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:28:25.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:28:25.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:28:25.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:28:25.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:28:25.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:28:25.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:28:25.028 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:28:33.982 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2c073386] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:28:33.984 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:28:33.984 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:28:33.984 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:28:34.003 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:28:34.003 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@79a2fe2]
2025-09-09 17:28:34.003 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:28:34.004 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2c073386] [3dca211a-38, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:28:34.004 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [2c073386] [3dca211a-38, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:28:35.003 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:28:35.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:28:35.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:28:35.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:28:35.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:28:35.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:28:35.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:28:35.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:28:35.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:28:35.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:28:35.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:28:35.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:28:35.021 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:28:37.280 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connection refused: no further information

java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:28:45.002 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:28:45.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:28:45.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:28:45.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:28:45.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:28:45.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:28:45.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:28:45.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:28:45.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:28:45.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:28:45.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:28:45.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:28:45.020 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:28:53.978 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [5dfe1f4] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:28:53.979 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:28:53.979 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:28:53.979 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:28:54.000 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:28:54.000 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@31c9a094]
2025-09-09 17:28:54.000 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:28:54.000 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [5dfe1f4] [3dca211a-39, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:28:54.000 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [5dfe1f4] [3dca211a-39, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:28:55.012 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:28:55.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:28:55.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:28:55.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:28:55.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:28:55.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:28:55.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:28:55.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:28:55.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:28:55.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:28:55.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:28:55.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:28:55.032 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:29:05.012 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:29:05.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:29:05.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:29:05.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:29:05.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:29:05.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:29:05.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:29:05.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:29:05.032 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:29:05.032 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:29:05.032 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:29:05.032 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:29:05.032 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:29:09.374 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connection refused: no further information

java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:29:13.981 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [64bec998] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:29:13.981 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:29:13.981 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:29:13.982 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:29:13.999 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:29:13.999 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@4b8df031]
2025-09-09 17:29:13.999 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:29:13.999 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [64bec998] [3dca211a-40, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:29:14.000 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [64bec998] [3dca211a-40, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:29:15.004 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:29:15.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:29:15.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:29:15.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:29:15.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:29:15.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:29:15.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:29:15.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:29:15.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:29:15.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:29:15.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:29:15.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:29:15.025 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:29:25.012 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:29:25.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:29:25.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:29:25.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:29:25.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:29:25.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:29:25.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:29:25.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:29:25.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:29:25.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:29:25.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:29:25.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:29:25.031 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:29:33.969 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [57ce9566] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:29:33.971 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:29:33.971 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:29:33.971 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:29:33.991 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:29:33.991 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@6e506fe6]
2025-09-09 17:29:33.992 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:29:33.992 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [57ce9566] [3dca211a-41, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:29:33.992 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [57ce9566] [3dca211a-41, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:29:35.005 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:29:35.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:29:35.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:29:35.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:29:35.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:29:35.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:29:35.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:29:35.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:29:35.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:29:35.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:29:35.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:29:35.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:29:35.023 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:29:42.395 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connect timed out

java.net.SocketTimeoutException: Connect timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:551)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:29:45.008 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:29:45.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:29:45.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:29:45.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:29:45.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:29:45.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:29:45.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:29:45.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:29:45.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:29:45.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:29:45.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:29:45.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:29:45.029 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:29:53.969 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [4810f236] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:29:53.970 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:29:53.970 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:29:53.970 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:29:53.992 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:29:53.992 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@4370ee03]
2025-09-09 17:29:53.992 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:29:53.992 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [4810f236] [3dca211a-42, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:29:53.992 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [4810f236] [3dca211a-42, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:29:55.007 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:29:55.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:29:55.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:29:55.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:29:55.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:29:55.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:29:55.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:29:55.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:29:55.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:29:55.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:29:55.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:29:55.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:29:55.027 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:30:05.001 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:30:05.003 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:30:05.003 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:30:05.004 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:30:05.004 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:30:05.004 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:30:05.004 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:30:05.004 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:30:05.006 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:30:05.006 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:30:05.006 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:30:05.006 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:30:05.006 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:30:13.975 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [5fac513e] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:30:13.976 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:30:13.976 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:30:13.976 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:30:13.995 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:30:13.995 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@75f96715]
2025-09-09 17:30:13.995 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:30:13.996 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [5fac513e] [3dca211a-43, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:30:13.996 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [5fac513e] [3dca211a-43, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:30:14.528 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connection refused: no further information

java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:30:15.008 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:30:15.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:30:15.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:30:15.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:30:15.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:30:15.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:30:15.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:30:15.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:30:15.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:30:15.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:30:15.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:30:15.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:30:15.026 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:30:23.985 | [39mDEBUG 13944[0;39m | [1;33mparallel-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [5aae2aaf] HTTP GET http://127.0.0.1:11000/actuator/info
2025-09-09 17:30:23.986 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/info", parameters={}
2025-09-09 17:30:23.986 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'info'
2025-09-09 17:30:23.986 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:30:23.986 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:30:23.986 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Writing [{}]
2025-09-09 17:30:23.986 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:30:23.986 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [5aae2aaf] [3dca211a-44, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:30:23.986 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [5aae2aaf] [3dca211a-44, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{}]
2025-09-09 17:30:25.013 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:30:25.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:30:25.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:30:25.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:30:25.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:30:25.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:30:25.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:30:25.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:30:25.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:30:25.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:30:25.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:30:25.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:30:25.031 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:30:33.970 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [6b575980] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:30:33.971 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:30:33.971 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:30:33.972 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:30:33.992 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:30:33.992 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@547572ef]
2025-09-09 17:30:33.992 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:30:33.993 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [6b575980] [3dca211a-45, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:30:33.993 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [6b575980] [3dca211a-45, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:30:35.002 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:30:35.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:30:35.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:30:35.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:30:35.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:30:35.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:30:35.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:30:35.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:30:35.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:30:35.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:30:35.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:30:35.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:30:35.022 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:30:45.000 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:30:45.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:30:45.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:30:45.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:30:45.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:30:45.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:30:45.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:30:45.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:30:45.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:30:45.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:30:45.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:30:45.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:30:45.020 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:30:46.586 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connection refused: no further information

java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:30:53.976 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2a3a65c8] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:30:53.977 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:30:53.977 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:30:53.977 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:30:53.999 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:30:54.000 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@5e745866]
2025-09-09 17:30:54.000 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:30:54.000 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2a3a65c8] [3dca211a-46, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:30:54.000 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [2a3a65c8] [3dca211a-46, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:30:55.005 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:30:55.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:30:55.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:30:55.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:30:55.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:30:55.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:30:55.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:30:55.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:30:55.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:30:55.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:30:55.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:30:55.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:30:55.024 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:31:05.004 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:31:05.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:31:05.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:31:05.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:31:05.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:31:05.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:31:05.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:31:05.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:31:05.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:31:05.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:31:05.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:31:05.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:31:05.025 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:31:13.978 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [1fc3ee72] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:31:13.979 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:31:13.979 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:31:13.980 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:31:14.002 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:31:14.002 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@1e29be1c]
2025-09-09 17:31:14.002 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:31:14.002 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [1fc3ee72] [3dca211a-47, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:31:14.002 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [1fc3ee72] [3dca211a-47, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:31:15.000 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:31:15.016 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:31:15.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:31:15.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:31:15.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:31:15.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:31:15.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:31:15.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:31:15.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:31:15.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:31:15.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:31:15.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:31:15.019 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:31:18.643 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connection refused: no further information

java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:31:25.005 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:31:25.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:31:25.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:31:25.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:31:25.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:31:25.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:31:25.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:31:25.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:31:25.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:31:25.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:31:25.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:31:25.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:31:25.026 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:31:33.982 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [17bd6fc7] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:31:33.983 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:31:33.983 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:31:33.983 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:31:34.002 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:31:34.002 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@2543bfae]
2025-09-09 17:31:34.003 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:31:34.003 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [17bd6fc7] [3dca211a-48, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:31:34.003 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [17bd6fc7] [3dca211a-48, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:31:34.999 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:31:35.015 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:31:35.016 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:31:35.016 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:31:35.016 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:31:35.016 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:31:35.016 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:31:35.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:31:35.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:31:35.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:31:35.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:31:35.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:31:35.017 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:31:45.006 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:31:45.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:31:45.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:31:45.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:31:45.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:31:45.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:31:45.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:31:45.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:31:45.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:31:45.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:31:45.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:31:45.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:31:45.028 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:31:50.783 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connection refused: no further information

java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:31:53.979 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [50d00f24] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:31:53.980 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:31:53.980 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:31:53.981 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:31:54.000 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:31:54.000 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@48bd9224]
2025-09-09 17:31:54.000 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:31:54.001 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [50d00f24] [3dca211a-49, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:31:54.001 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [50d00f24] [3dca211a-49, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:31:54.999 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:31:55.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:31:55.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:31:55.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:31:55.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:31:55.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:31:55.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:31:55.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:31:55.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:31:55.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:31:55.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:31:55.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:31:55.020 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:32:04.999 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:32:05.015 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:32:05.015 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:32:05.015 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:32:05.015 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:32:05.015 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:32:05.015 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:32:05.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:32:05.017 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:32:05.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:32:05.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:32:05.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:32:05.018 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:32:13.976 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [437ab2e1] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:32:13.977 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:32:13.977 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:32:13.977 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:32:13.995 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:32:13.995 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@128b31a6]
2025-09-09 17:32:13.995 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:32:13.995 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [437ab2e1] [3dca211a-50, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:32:13.997 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [437ab2e1] [3dca211a-50, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:32:15.007 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:32:15.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:32:15.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:32:15.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:32:15.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:32:15.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:32:15.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:32:15.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:32:15.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:32:15.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:32:15.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:32:15.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:32:15.030 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:32:22.866 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connection refused: no further information

java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:32:23.985 | [39mDEBUG 13944[0;39m | [1;33mparallel-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [45991147] HTTP GET http://127.0.0.1:11000/actuator/info
2025-09-09 17:32:23.986 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/info", parameters={}
2025-09-09 17:32:23.986 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'info'
2025-09-09 17:32:23.986 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:32:23.986 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:32:23.986 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Writing [{}]
2025-09-09 17:32:23.986 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:32:23.986 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [45991147] [3dca211a-51, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:32:23.986 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [45991147] [3dca211a-51, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{}]
2025-09-09 17:32:25.003 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:32:25.020 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:32:25.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:32:25.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:32:25.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:32:25.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:32:25.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:32:25.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:32:25.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:32:25.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:32:25.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:32:25.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:32:25.022 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:32:33.978 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2e3a6ba5] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:32:33.978 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:32:33.978 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:32:33.978 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:32:34.003 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:32:34.003 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@44d6c9c0]
2025-09-09 17:32:34.003 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:32:34.003 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [2e3a6ba5] [3dca211a-52, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:32:34.004 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [2e3a6ba5] [3dca211a-52, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:32:35.010 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:32:35.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:32:35.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:32:35.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:32:35.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:32:35.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:32:35.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:32:35.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:32:35.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:32:35.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:32:35.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:32:35.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:32:35.031 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:32:45.012 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:32:45.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:32:45.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:32:45.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:32:45.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:32:45.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:32:45.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:32:45.031 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:32:45.032 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:32:45.032 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:32:45.032 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:32:45.032 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:32:45.032 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:32:53.972 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [83df229] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:32:53.974 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:32:53.974 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:32:53.974 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:32:53.993 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:32:53.993 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@5277c735]
2025-09-09 17:32:53.994 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:32:53.994 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [83df229] [3dca211a-53, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:32:53.994 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [83df229] [3dca211a-53, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:32:54.925 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connection refused: no further information

java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:32:55.007 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:32:55.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:32:55.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:32:55.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:32:55.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:32:55.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:32:55.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:32:55.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:32:55.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:32:55.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:32:55.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:32:55.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:32:55.027 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:33:05.006 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:33:05.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:33:05.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:33:05.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:33:05.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:33:05.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:33:05.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:33:05.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:33:05.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:33:05.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:33:05.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:33:05.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:33:05.026 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:33:13.972 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [77208c32] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:33:13.974 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:33:13.974 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:33:13.974 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:33:14.003 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:33:14.003 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@11ba1fbd]
2025-09-09 17:33:14.003 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-5 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:33:14.003 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [77208c32] [3dca211a-54, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:33:14.004 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [77208c32] [3dca211a-54, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:33:15.008 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:33:15.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:33:15.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:33:15.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:33:15.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:33:15.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:33:15.025 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:33:15.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:33:15.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:33:15.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:33:15.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:33:15.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-6 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:33:15.026 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:33:23.992 | [39mDEBUG 13944[0;39m | [1;33mparallel-2 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [3639f8e8] HTTP GET http://127.0.0.1:11000/actuator/info
2025-09-09 17:33:23.993 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/info", parameters={}
2025-09-09 17:33:23.993 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'info'
2025-09-09 17:33:23.993 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:33:23.993 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:33:23.994 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Writing [{}]
2025-09-09 17:33:23.994 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-7 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:33:23.994 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [3639f8e8] [3dca211a-55, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:33:23.994 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [3639f8e8] [3dca211a-55, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{}]
2025-09-09 17:33:25.000 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:33:25.018 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:33:25.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:33:25.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:33:25.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:33:25.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:33:25.019 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:33:25.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:33:25.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:33:25.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:33:25.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:33:25.021 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-8 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:33:25.021 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:33:27.019 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connection refused: no further information

java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:33:33.970 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [64fd517] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:33:33.972 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:33:33.972 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:33:33.972 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:33:33.994 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:33:33.994 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@68d6e346]
2025-09-09 17:33:33.994 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-9 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:33:33.994 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [64fd517] [3dca211a-56, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:33:33.995 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [64fd517] [3dca211a-56, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:33:35.008 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:33:35.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:33:35.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:33:35.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:33:35.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:33:35.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:33:35.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:33:35.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:33:35.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:33:35.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:33:35.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:33:35.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-10 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:33:35.027 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:33:45.010 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:33:45.026 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:33:45.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:33:45.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:33:45.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:33:45.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:33:45.027 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:33:45.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:33:45.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:33:45.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:33:45.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:33:45.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-2 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:33:45.029 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:33:53.981 | [39mDEBUG 13944[0;39m | [1;33mparallel-1 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [5696cccc] HTTP GET http://127.0.0.1:11000/actuator/health
2025-09-09 17:33:53.983 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | GET "/actuator/health", parameters={}
2025-09-09 17:33:53.983 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32ms.b.a.e.w.s.WebMvcEndpointHandlerMapping[0;39m | Mapped to Actuator web endpoint 'health'
2025-09-09 17:33:53.983 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/octet-stream" to []
2025-09-09 17:33:54.002 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/vnd.spring-boot.actuator.v3+json', given [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/vnd.spring-boot.actuator.v1+json, application/json] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-09-09 17:33:54.002 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [org.springframework.boot.actuate.health.SystemHealth@400ac507]
2025-09-09 17:33:54.003 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-1 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed 200 OK
2025-09-09 17:33:54.003 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.w.r.f.client.ExchangeFunctions      [0;39m | [5696cccc] [3dca211a-57, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Response 200 OK
2025-09-09 17:33:54.003 | [39mDEBUG 13944[0;39m | [1;33mreactor-http-nio-13 [TID: N/A][0;39m [1;32mo.s.http.codec.json.Jackson2JsonDecoder [0;39m | [5696cccc] [3dca211a-57, L:/127.0.0.1:59463 - R:/127.0.0.1:11000] Decoded [{status=UP}]
2025-09-09 17:33:55.011 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:33:55.028 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:33:55.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:33:55.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:33:55.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:33:55.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:33:55.029 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:33:55.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:33:55.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:33:55.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:33:55.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:33:55.030 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-3 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:33:55.031 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
2025-09-09 17:33:59.124 | [1;31mERROR 13944[0;39m | [1;33mxxl-job, executor ExecutorRegistryThread [TID: N/A][0;39m [1;32mc.xxl.job.core.util.XxlJobRemotingUtil  [0;39m | Connection refused: no further information

java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:534)
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:639)
	at java.base/sun.net.www.http.HttpClient.<init>(HttpClient.java:282)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:387)
	at java.base/sun.net.www.http.HttpClient.New(HttpClient.java:409)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1308)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1241)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1127)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1056)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:46)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-09-09 17:34:05.005 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Writing [Application(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0.1:11000/actuator/health, serviceUrl=http://127.0.0.1:11000/admin/monitor)] as "application/json" with org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
2025-09-09 17:34:05.022 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | POST "/admin/monitor/instances", parameters={}
2025-09-09 17:34:05.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mm.m.a.RequestResponseBodyMethodProcessor[0;39m | Read "application/json;charset=UTF-8" to [Registration(name=ym-server, managementUrl=http://127.0.0.1:11000/actuator, healthUrl=http://127.0.0 (truncated)...]
2025-09-09 17:34:05.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Started async request for "/admin/monitor/instances"
2025-09-09 17:34:05.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Async result set for "/admin/monitor/instances"
2025-09-09 17:34:05.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.c.request.async.WebAsyncManager   [0;39m | Performing async dispatch for "/admin/monitor/instances"
2025-09-09 17:34:05.023 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting but response remains open for further handling
2025-09-09 17:34:05.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | "ASYNC" dispatch for POST "/admin/monitor/instances", parameters={}
2025-09-09 17:34:05.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32ms.w.s.m.m.a.RequestMappingHandlerAdapter[0;39m | Resume with async result [<201 CREATED Created,{id=e50ef2fda688},[Location:"http://127.0.0.1:11000/instances/e50ef2fda688"]>]
2025-09-09 17:34:05.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/yaml]
2025-09-09 17:34:05.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.w.s.m.m.a.HttpEntityMethodProcessor [0;39m | Writing [{id=e50ef2fda688}]
2025-09-09 17:34:05.024 | [39mDEBUG 13944[0;39m | [1;33mhttp-nio-11000-exec-4 [TID: N/A][0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Exiting from "ASYNC" dispatch, status 201
2025-09-09 17:34:05.024 | [39mDEBUG 13944[0;39m | [1;33mregistrationTask1 [TID: N/A][0;39m [1;32mo.s.web.client.DefaultRestClient        [0;39m | Reading to [java.util.Map<java.lang.String, java.lang.Object>]
