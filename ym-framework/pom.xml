<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>ym</artifactId>
        <groupId>com.fozmo</groupId>
        <version>1.0</version>
    </parent>
    <packaging>pom</packaging>
    <description>ym 框架</description>
    <modules>
        <module>ym-common</module>
        <module>ym-spring-boot-starter-mybatis</module>
        <module>ym-spring-boot-starter-redis</module>
        <module>ym-spring-boot-starter-web</module>
        <module>ym-spring-boot-starter-security</module>

        <module>ym-spring-boot-starter-monitor</module>
        <module>ym-spring-boot-starter-protection</module>
        <module>ym-spring-boot-starter-job</module>
        <module>ym-spring-boot-starter-mq</module>

        <module>ym-spring-boot-starter-excel</module>
        <module>ym-spring-boot-starter-test</module>

        <module>ym-spring-boot-starter-biz-tenant</module>
        <module>ym-spring-boot-starter-biz-data-permission</module>
        <module>ym-spring-boot-starter-biz-ip</module>
        <module>ym-common-sensitive</module>
    </modules>

    <artifactId>ym-framework</artifactId>

</project>
