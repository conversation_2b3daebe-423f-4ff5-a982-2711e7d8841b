<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.fozmo</groupId>
        <artifactId>ym-framework</artifactId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>ym-spring-boot-starter-excel</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>Excel 拓展</description>
    <url></url>

    <dependencies>
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-common</artifactId>
        </dependency>

        <!-- Spring 核心 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-module-system-api</artifactId> <!-- 需要使用它，进行 Dict 的查询 -->
            <version>1.0</version>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <scope>provided</scope> <!-- 设置为 provided，只有 ExcelUtils 使用 -->
        </dependency>

        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <scope>provided</scope> <!-- 设置为 provided，只有 ExcelUtils 使用 -->
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId> <!-- 解决 https://github.com/alibaba/easyexcel/issues/3954 问题 -->
        </dependency>

        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-biz-ip</artifactId>
            <optional>true</optional> <!-- 设置为 optional，只有在 AreaConvert 的时候使用 -->
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.fozmo</groupId>
            <artifactId>ym-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
